{"name": "firefly-be", "version": "1.0.0", "description": "", "main": "src/index.js", "scripts": {"watch": "nodemon index.ts", "start": "node -max_old_space_size=4096 -r dotenv/config dist/index.js", "db:migrate:latest": "knex --knexfile db/knexfile.ts migrate:latest", "db:migrate:make": "knex --knexfile db/knexfile.ts migrate:make", "build": "tsc"}, "author": "<PERSON><PERSON>", "license": "ISC", "devDependencies": {"@tsconfig/node16": "^1.0.3", "@types/bcrypt": "^5.0.0", "@types/cookie-parser": "^1.4.3", "@types/cron": "^2.0.0", "@types/express": "^4.17.13", "@types/jsonwebtoken": "^8.5.8", "@types/node": "^18.0.0", "@types/uuid": "^8.3.4", "@typescript-eslint/eslint-plugin": "^5.29.0", "@typescript-eslint/parser": "^5.29.0", "eslint": "^7.32.0 || ^8.2.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-import": "^2.25.2", "nodemon": "^2.0.18", "prettier": "^2.7.1", "ts-node": "^10.8.1", "typescript": "^4.7.4"}, "dependencies": {"@sendgrid/mail": "^7.7.0", "@types/cron": "^2.0.1", "axios": "^0.27.2", "bcrypt": "^5.1.0", "cookie-parser": "^1.4.6", "cron": "^2.3.0", "dotenv": "^16.0.1", "express": "^4.18.1", "jsonwebtoken": "^8.5.1", "knex": "^2.3.0", "objection": "^3.0.1", "pg": "^8.8.0", "rambda": "^7.2.1", "redis": "^4.6.7", "uuid": "^9.0.0"}}
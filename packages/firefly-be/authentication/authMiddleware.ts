import express from "express";
import jwt from "jsonwebtoken";
import User from "../models/user/UserModel";
import { getAccessToken } from "./helpers";
import authRouter from "./router";
import { UserRequest } from "./type";

const router = express.Router();

router.use((req: UserRequest, res, next) => {
  if (!req.path.includes("/auth/")) {
    const accessToken = getAccessToken(req);
    if (!accessToken) {
      next();
    } else {
      jwt.verify(accessToken, process.env.ACCESS_TOKEN_SECRET!, (err, user) => {
        if (err) {
          res.status(401).end();
          return;
        }
        req.user = user as User;
        next(err);
      });
    }
  } else {
    next();
  }
});

router.use("/auth", authRouter);

export default router;

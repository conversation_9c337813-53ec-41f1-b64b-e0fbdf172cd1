import express from "express";
import jwt from "jsonwebtoken";
import User from "../models/user/UserModel";
import { UserService } from "../models/user/UserService";
import { getNewToken, isPasswordGoodEnough } from "./helpers";
import * as reservanto from "../reservanto/reservantoOperations";
import { RequestError } from "../errors/types";
import { v4 as uuidv4 } from "uuid";
import { sendEmail } from "../email/emailSender";
import bcrypt from "bcrypt";

const router = express.Router();

router.post("/login", async (req, res, next) => {
  const { body } = req;
  try {
    if (body.email == null || body.password == null) {
      throw new RequestError({ code: 400, message: "Chybí vstupní hodnoty" });
    }
    const user = await UserService.getUser({ email: body.email });
    if (!user) {
      throw new RequestError({
        code: 400,
        message: "Špatný e-mail nebo heslo.",
      });
    }

    const isPasswordValid = await bcrypt.compare(
      body.password,
      user.passwordHash!
    );

    if (isPasswordValid) {
      const accessToken = getNewToken("access", {
        id: user.id!,
        email: user.email!,
      });
      const refreshToken = getNewToken("refresh", {
        id: user.id!,
        email: user.email!,
      });

      res.json({ accessToken, refreshToken });
    } else {
      return next(
        new RequestError({ code: 400, message: "Špatný e-mail nebo heslo." })
      );
    }
  } catch (e) {
    next(e);
  }
});

router.post("/refresh-token", (req, res, next) => {
  const { refreshToken } = req.body;
  jwt.verify(
    refreshToken as string,
    process.env.REFRESH_TOKEN_SECRET!,
    (err, user) => {
      if (err)
        return next(new RequestError({ code: 400, message: "Unauthorized" }));
      const { email, id } = user as User;
      const accessToken = getNewToken("access", { email, id });
      res.json({ accessToken });
    }
  );
});

router.put("/set-new-password", async (req, res, next) => {
  try {
    const { body } = req;

    if (!isPasswordGoodEnough(body.password)) {
      throw new RequestError({
        code: 400,
        message:
          "Heslo musí obsahovat alespoň 6 znaků, z toho jedno velký znak, jeden malý a jedno číslo.",
      });
    }
    const passwordHash = bcrypt.hashSync(body.password, 8);
    await UserService.updateUser(
      { passwordHash, resetPasswordToken: null },
      { resetPasswordToken: body.token }
    );
    res.end();
  } catch (e) {
    next(e);
  }
});

router.get("/is-user-valid-by-token", async (req, res, next) => {
  try {
    const { token } = req.query;
    if (token) {
      const user = await UserService.getUser({
        resetPasswordToken: token as string,
      });
      if (user) {
        res.end();
        return;
      }
    }
    throw new RequestError({
      code: 400,
      message: "Nevalidní odkaz",
    });
  } catch (e) {
    next(e);
  }
});

router.post("/reset-password", async (req, res, next) => {
  try {
    const { email } = req.body;
    const resetPasswordToken = uuidv4();
    const user = await UserService.updateUser(
      { resetPasswordToken },
      { email }
    );
    if (user) {
      sendEmail({
        to: email,
        type: "resetPassword",
        values: {
          resetPasswordLink: `${process.env.APP_URL}/reset-hesla?token=${resetPasswordToken}`,
        },
      });
    }
    res.end(JSON.stringify({ email }));
  } catch (e) {
    next(e);
  }
});

router.post("/register", async (req, res, next) => {
  const { body } = req;
  try {
    if (body.email == null || body.password == null) {
      throw new RequestError({
        code: 400,
        message: "Chybí požadovené hodnoty.",
      });
    }
    if (!isPasswordGoodEnough(body.password)) {
      throw new RequestError({
        code: 400,
        message:
          "Heslo musí obsahovat alespoň 6 znaků, z toho jedno velký znak, jeden malý a jedno číslo.",
      });
    }
    const [firstName, ...lastName] = body.name.split(" ");
    delete body.name;

    const foundUser = await UserService.getUser({ email: body.email });
    if (foundUser) {
      throw new RequestError({
        code: 400,
        message: "Tento uživatel již existuje",
      });
    }

    const response = await reservanto.createCustomer({
      firstName,
      lastName: lastName.join(" "),
      ...body,
    });
    const passwordHash = bcrypt.hashSync(body.password, 8);
    try {
      await UserService.createUser({
        email: body.email,
        passwordHash,
        id: response.Customer.Id.toString(),
      });
    } catch (e) {
      throw new RequestError({
        code: 400,
        message: "Tento uživatel již existuje",
      });
    }
    res.status(200).send();
  } catch (e) {
    next(e);
  }
});
export default router;

import { Request } from "express";
import jwt from "jsonwebtoken";

export const passwordRegex = /^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9]).{6,}$/;

export const getAccessToken = (req: Request): string | null => {
  const accessToken = req.cookies["fat"];
  if (accessToken) {
    return accessToken;
  }
  return null;
};

export const getNewToken = (
  type: "refresh" | "access",
  user: { id: string; email: string }
) => {
  if (type === "refresh") {
    return jwt.sign(user, process.env.REFRESH_TOKEN_SECRET!, {
      expiresIn: "200d",
    });
  }
  return jwt.sign(user, process.env.ACCESS_TOKEN_SECRET!, { expiresIn: "45m" });
};

export const isPasswordGoodEnough = (password: string) => {
  return passwordRegex.test(password);
};

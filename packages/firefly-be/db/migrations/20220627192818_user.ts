import { Knex } from "knex";


export async function up(knex: Knex): Promise<void> {
    await knex.schema.createTable('user', table => {
        table.increments('id');
        table.string('email').notNullable()
        table.text('password_hash')
        table.timestamps(false, true);
    });

    await knex('user').insert({ 
        email: "<EMAIL>", 
        // firefly
        password_hash: "$argon2i$v=19$m=4096,t=3,p=1$+E8bOtrvUQB9tUcvE0G5mQ$aX2f86ytlvmcQD+/WmDYQ2skHlqRkbcUvKpMQ8JeJ4E"
    });
}


export async function down(knex: Knex): Promise<void> {
    return knex.schema.dropTable('user',)
}


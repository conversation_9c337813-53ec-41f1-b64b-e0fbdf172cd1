import type { K<PERSON> } from "knex";
import { knexSnakeCaseMappers } from "objection";
import * as path from "path";
import { parse } from "pg-connection-string";

require("dotenv").config({ path: "../.env" });
const envConfig = parse(process.env.DATABASE_URL || "");

const config: { [key: string]: Knex.Config } = {
  development: {
    client: "postgresql",
    connection: {
      database: "firefly",
      user: "postgres",
      password: "admin",
    },
    searchPath: ['reservanto'],
    migrations: {
      tableName: "knex_migrations",
      loadExtensions: [".js", ".ts"],
      directory: path.join(__dirname, "migrations"),
    },
    ...knexSnakeCaseMappers(),
  },

  staging: {
    client: "postgresql",
    connection: {
      database: envConfig.database || "",
      user: envConfig.user,
      password: envConfig.password || "",
      host: envConfig.host || "",
      port: envConfig.port ? Number(envConfig.port) : 4000,
    },
    searchPath: ['reservanto'],
    migrations: {
      tableName: "knex_migrations",
      loadExtensions: [".js", ".ts"],
      directory: path.join(__dirname, "migrations"),
    },
    ...knexSnakeCaseMappers(),
  },

  production: {
    client: "postgresql",
    connection: {
      database: envConfig.database || "",
      user: envConfig.user,
      host: envConfig.host || "",
      password: envConfig.password || "",
      port: envConfig.port ? Number(envConfig.port) : 4000,
    },
    pool: {
      min: 2,
      max: 10,
    },
    searchPath: ['reservanto'],
    migrations: {
      tableName: "knex_migrations",
      loadExtensions: [".js", ".ts"],
      directory: path.join(__dirname, "migrations"),
    },
    ...knexSnakeCaseMappers(),
  },
};

export default config;

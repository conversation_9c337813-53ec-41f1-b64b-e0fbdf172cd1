{
    "env": {
        "browser": true,
        "es2021": true
    },
    "extends": [
        "airbnb-base",
        "prettier",
        "plugin:import/typescript",
    ],
    "parser": "@typescript-eslint/parser",
    "parserOptions": {
        "ecmaVersion": "latest",
        "sourceType": "module"
    },
    "plugins": [
        "@typescript-eslint",
        "import"
    ],
    "rules": {
        "quotes": ["error", "single"],
        // we want to force semicolons
        "semi": ["error", "always"],
        // we use 2 spaces to indent our code
        "indent": ["error", 2],
        // we want to avoid extraneous spaces
        "no-multi-spaces": ["error"],
        // "import/extensions": [
        //     "error",
        //     "ignorePackages",
        //     {
        //       "js": "never",
        //       "mjs": "never",
        //       "jsx": "never",
        //       "ts": "never",
        //       "tsx": "never"
        //     }
        // ]
    }
}

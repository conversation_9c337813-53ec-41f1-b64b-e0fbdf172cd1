import sgMail from "@sendgrid/mail";
import { templates } from "./templates";

if (process.env.SENDGRID_API_KEY) {
  sgMail.setApiKey(process.env.SENDGRID_API_KEY);
}

const getHtml = (html: string, values?: Record<string, any>) => {
  if (!values) {
    return html;
  }

  let htmlText = html;
  Object.keys(values).forEach((key) => {
    const correctKey = `{{${key}}}`;
    const rg = new RegExp(correctKey, "gi");
    htmlText = htmlText.replace(rg, values[key]);
  });

  return htmlText;
};

export const sendEmail = ({
  to,
  type,
  values,
}: {
  to: string;
  type: keyof typeof templates;
  values?: Record<string, any>;
}) => {
  const msg = {
    to,
    from: {
      email: "<EMAIL>",
      name: "Firefly Studio"
    },
    subject: templates[type].subject,
    html: getHtml(templates[type].html, values),
  };

  console.log(`Sending email ${templates[type].subject} to ${to}`);
  sgMail
    .send(msg)
    .then(() => {
      console.log("Email was successfully sent");
    })
    .catch((error) => {
      console.error(error);
      console.log(error.response.body.errors);
    });
};

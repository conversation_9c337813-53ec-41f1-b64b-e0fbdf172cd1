import express from "express";
import { RequestError } from "../errors/types";
import * as strapi from "../strapi/strapiOperations";
import { getFilteredAppointments } from "./helpers/getFilteredAppointments";

const router = express.Router();

router.get("/forLesson", async (req, res) => {
  const { bookingServiceId, from, to } = req.query;

  let appointments = await getFilteredAppointments({
    fromInterval: Number(from),
    toInterval: Number(to),
  });

  res.json(
    appointments.filter(
      (appointment) => appointment.BookingServiceId === Number(bookingServiceId)
    )
  );
});

router.get("/", async (req, res, next) => {
  const {
    destinationId,
    activityIds,
    lessonTypeIds,
    isForBeginners,
    from,
    to,
  } = req.query;

  try {
    if (!from || !to) {
      throw new RequestError({ code: 400, message: "Missing interval params" });
    }

    let [appointments, lessonsResponse] = await Promise.all([
      getFilteredAppointments({
        fromInterval: Number(from),
        toInterval: Number(to),
      }),
      strapi.getLessons({
        lessonTypeIds: lessonTypeIds as string[],
        isForBeginners: isForBeginners as string,
        activityIds: activityIds as string[],
      }),
    ]);

    if (destinationId) {
      appointments = appointments.filter((a) =>
        (destinationId as string).split("-").includes(a.CalendarId.toString())
      );
    }

    const bookingServiceIds = lessonsResponse?.data?.map(
      (l) => l.attributes.bookingServiceId
    );

    res.json(
      appointments.filter((ap) =>
        bookingServiceIds?.includes(ap.BookingServiceId)
      )
    );
  } catch (e) {
    next(e);
  }
});

export default router;

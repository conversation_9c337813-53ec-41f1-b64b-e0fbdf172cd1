import express from "express";
import { UserRequest } from "../authentication/type";
import { sendEmail } from "../email/emailSender";
import { RequestError } from "../errors/types";
import { availablePastoPay, getTimeInUnixTimeStamp, getVS } from "../helpers";
import * as reservanto from "../reservanto/reservantoOperations";
import { Appointment } from "../reservanto/types";
import * as strapi from "../strapi/strapiOperations";
import { getFilteredAppointments } from "./helpers/getFilteredAppointments";
import {
  getInfoForBookingCourseConfirmEmail,
  getInfoForBookingLessonConfirmEmail,
  getInfoForBookingShortCourseConfirmEmail,
} from "./helpers/getInfoForBookingConfirmEmail";

const router = express.Router();

router.post("/alternate", async (req: UserRequest, res, next) => {
  const { courseId, appointmentId, customerId } = req.body;
  const { Customer: customer } = await reservanto.getCustomer(customerId);

  const fromNow = getTimeInUnixTimeStamp(new Date());
  let appointments = await getFilteredAppointments({
    fromInterval: fromNow,
    toInterval: fromNow + 20000000,
  });

  const appointment = appointments.find((a) => a.Id === appointmentId);
  if (!appointment) {
    throw new RequestError({
      code: 400,
      message: "Could not find appointment",
    });
  }

  try {
    if (customerId == null) {
      return next(
        new RequestError({
          code: 400,
          message:
            "Není specifikovaný zákazník pro kterehé se má rezervace vytvořit",
        })
      );
    }

    if (courseId) {
      await reservanto.addToAlternateCourse({ courseId, customerId });
      sendEmail({
        to: customer?.Email!,
        type: "alternateBookingConfirmation",
        values: {
          name: appointment.BookingServiceName,
        },
      });
    } else {
      await reservanto.addToAlternateAppointment({ appointmentId, customerId });
      sendEmail({
        to: customer?.Email!,
        type: "alternateBookingConfirmation",
        values: {
          name: appointment.BookingServiceName,
        },
      });
    }
    res.status(200).send();
  } catch (err) {
    console.error(err);
    return next(new RequestError({ code: 400, message: "Něco se pokazilo" }));
  }
});

router.post("/", async (req: UserRequest, res, next) => {
  const { customerNote, customerId, appointmentId } = req.body;

  if (customerId == null) {
    return next(
      new RequestError({
        code: 400,
        message:
          "Není specifikovaný zákazník pro kterehé se má rezervace vytvořit",
      })
    );
  }

  const fromNow = getTimeInUnixTimeStamp(new Date());
  let appointments = await getFilteredAppointments({
    fromInterval: fromNow,
    toInterval: fromNow + 20000000,
  });

  const { Customer: customer } = await reservanto.getCustomer(customerId);
  const variableSymbol = getVS(customer.Phone);

  const appointment = appointments.find((a) => a.Id === appointmentId);
  if (!appointment) {
    throw new RequestError({
      code: 400,
      message: "Could not find appointment",
    });
  }

  const relatedAppointments: Appointment[] = [];
  if (appointment?.CourseId) {
    const courseAppointments = appointments.filter(
      (a) =>
        a.CourseId === appointment.CourseId &&
        a.BookingServiceId === appointment.BookingServiceId
    );
    relatedAppointments.push(...courseAppointments);
  } else {
    relatedAppointments.push(appointment);
  }

  try {
    // if it's lesson or open space, else is course
    if (relatedAppointments.length === 1) {
      await reservanto.createBooking({
        appointmentId,
        customerNote,
        customerId,
      });
      const customerResponse = await reservanto.getCustomerDetails(
        Number(customerId)
      );

      if (
        !availablePastoPay(
          appointment.BookingServiceId,
          customerResponse.CustomerDetails
        )
      ) {
        const lessonResponse = await strapi.getLessonsByBookingService(
          String(appointment?.BookingServiceId),
          3
        );

        sendEmail({
          to: customer?.Email!,
          type: "bookingLessonConfirmation",
          values: getInfoForBookingLessonConfirmEmail(
            appointment,
            lessonResponse.data?.[0]?.attributes.price || 0,
            variableSymbol
          ),
        });
      }
    } else {
      const lessonResponse = await strapi.getLessonsByBookingService(
        String(relatedAppointments[0]?.BookingServiceId),
        3
      );
      await Promise.all(
        relatedAppointments.map(
          async (a) =>
            await reservanto.createBooking({
              appointmentId: a.Id,
              customerNote,
              customerId,
            })
        )
      );
      if (relatedAppointments.length > 2) {
        sendEmail({
          to: customer?.Email!,
          type: "bookingCourseConfirmation",
          values: getInfoForBookingCourseConfirmEmail(
            relatedAppointments,
            lessonResponse.data?.[0]?.attributes.price || 0,
            variableSymbol
          ),
        });
      } else {
        sendEmail({
          to: customer?.Email!,
          type: "bookingShortCourseConfirmation",
          values: getInfoForBookingShortCourseConfirmEmail(
            relatedAppointments,
            lessonResponse.data?.[0]?.attributes.price || 0,
            variableSymbol
          ),
        });
      }
    }
    res.status(200).send();
  } catch (e) {
    console.error(e);
    return next(new RequestError({ code: 400, message: "Něco se pokazilo" }));
  }
});

router.delete("/alternate", async (req: UserRequest, res, next) => {
  const { user } = req;
  const { appointmentId, courseId } = req.query;
  if (!user)
    return next(
      new RequestError({
        code: 401,
        message: "K této akci musíte být přihlášený",
      })
    );
  try {
    if (courseId) {
      await reservanto.removeFromAlternateCourse({
        courseId: Number(courseId),
        customerId: Number(user.id),
      });
    } else {
      await reservanto.removeFromAlternateAppointment({
        appointmentId: Number(appointmentId),
        customerId: Number(user.id),
      });
    }

    res.status(200).send();
  } catch (e) {
    console.error(e);
    return next(
      new RequestError({
        code: 400,
        message: "Něco se pokazilo",
      })
    );
  }
});

router.delete("/:appointmentId", async (req: UserRequest, res, next) => {
  const { user } = req;
  const { appointmentId } = req.params;
  const { enableCancelWholeCourse } = req.query;
  if (!user)
    return next(
      new RequestError({
        code: 401,
        message: "K této akci musíte být přihlášený",
      })
    );
  try {
    await reservanto.cancelBooking({
      appointmentId: Number(appointmentId),
      customerId: Number(user.id),
      enableCancelWholeCourse:
        enableCancelWholeCourse != null
          ? Boolean(enableCancelWholeCourse)
          : false,
    });
    res.status(200).send();
  } catch (e) {
    console.error(e);
    return next(
      new RequestError({
        code: 400,
        message: "Něco se pokazilo",
      })
    );
  }
});

export default router;

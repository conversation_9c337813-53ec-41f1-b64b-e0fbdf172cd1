import { Appointment } from "../../reservanto/types";

export const bankInfo = {
  "I. P. Pavlova - SÁL 1": {
    accountNumber: **********,
    bankCode: 2010,
    message: "<PERSON><PERSON><PERSON>",
  },
  "Studio Hradčanská": {
    accountNumber: **********,
    bankCode: 2010,
    message: "<PERSON><PERSON><PERSON>",
  },
  "I.P. Pavlova - SÁL 2": {
    accountNumber: **********,
    bankCode: 2010,
    message: "<PERSON><PERSON><PERSON>",
  },
};

export const getInfoForBookingLessonConfirmEmail = (
  appointment: Appointment,
  lessonPrice: number,
  variableSymbol: string
) => {
  const paymentInfo =
    bankInfo[appointment.CalendarName as keyof typeof bankInfo];

  const qrCodeLink = `http://api.paylibo.com/paylibo/generator/czech/image?accountNumber=${paymentInfo.accountNumber}&bankCode=${paymentInfo.bankCode}&amount=${lessonPrice}&currency=CZK&vs=${variableSymbol}&message=${paymentInfo.message}`;

  return {
    qrCodeLink,
    amount: lessonPrice,
    bankAccountNumber: `${paymentInfo.accountNumber}/${paymentInfo.bankCode}`,
    variableSymbol,
    name: appointment.BookingServiceName,
    lector: appointment.BookingResourceName,
    location: appointment.CalendarName,
  };
};

export const getInfoForBookingCourseConfirmEmail = (
  appointments: Appointment[],
  lessonPrice: number,
  variableSymbol: string
) => {
  const firstAppointment = appointments[0];

  const totalPrice = lessonPrice * appointments.length;

  const toPay = appointments.length > 2 ? 500 : totalPrice;

  const paymentInfo =
    bankInfo[firstAppointment.CalendarName as keyof typeof bankInfo];

  const qrCodeLink = `http://api.paylibo.com/paylibo/generator/czech/image?accountNumber=${paymentInfo.accountNumber}&bankCode=${paymentInfo.bankCode}&amount=${toPay}&currency=CZK&vs=${variableSymbol}&message=${paymentInfo.message}`;

  return {
    qrCodeLink,
    deposit: toPay,
    bankAccountNumber: `${paymentInfo.accountNumber}/${paymentInfo.bankCode}`,
    variableSymbol,
    name: firstAppointment.BookingServiceName,
    lector: firstAppointment.BookingResourceName,
    location: firstAppointment.CalendarName,
  };
};

export const getInfoForBookingShortCourseConfirmEmail = (
  appointments: Appointment[],
  lessonPrice: number,
  variableSymbol: string
) => {
  const firstAppointment = appointments[0];

  const totalPrice = lessonPrice * appointments.length;

  const toPay = appointments.length > 2 ? 500 : totalPrice;

  const paymentInfo =
    bankInfo[firstAppointment.CalendarName as keyof typeof bankInfo];

  const qrCodeLink = `http://api.paylibo.com/paylibo/generator/czech/image?accountNumber=${paymentInfo.accountNumber}&bankCode=${paymentInfo.bankCode}&amount=${toPay}&currency=CZK&vs=${variableSymbol}&message=${paymentInfo.message}`;

  return {
    qrCodeLink,
    amount: toPay,
    bankAccountNumber: `${paymentInfo.accountNumber}/${paymentInfo.bankCode}`,
    variableSymbol,
    name: firstAppointment.BookingServiceName,
    lector: firstAppointment.BookingResourceName,
    location: firstAppointment.CalendarName,
  };
};


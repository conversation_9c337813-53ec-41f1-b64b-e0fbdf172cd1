import * as reservanto from "../../reservanto/reservantoOperations";
import { Appointment } from "../../reservanto/types";

const segmentIds = [6, 1012];

interface GetFilteredAppointmentsProps {
  fromInterval: number;
  toInterval: number;
  destinationId?: number;
}

export const getFilteredAppointments = async ({
  fromInterval,
  toInterval,
}: GetFilteredAppointmentsProps): Promise<Appointment[]> => {
  try {
    const responses = await Promise.all(
      segmentIds.map(async (segmentId) =>
        reservanto.getAvailableAppointments(segmentId, fromInterval, toInterval)
      )
    );

    let allAvailableAppointments: Appointment[] = [];
    responses.forEach((res) => {
      allAvailableAppointments.push(...res.Items);
    });

    return allAvailableAppointments;
  } catch (e) {
    return [];
  }
};

import { Appointment, AlternateEvent } from "../../reservanto/types";

export const makeEventsFromAppointments = (
  appointments: Appointment[]
): AlternateEvent[] => {
  return appointments.map((appointment) => ({
    AppointmentId: appointment.Id,
    ServiceName: appointment.BookingServiceName,
    BookingServiceId: appointment.BookingServiceId,
    SourceName: appointment.BookingResourceName,
    CalendarId: appointment.CalendarId,
    CalendarName: appointment.CalendarName,
    StartDate: appointment.StartDate,
    EndDate: appointment.EndDate,
    Status: "waiting",
    Price: appointment.Price,
    CourseId: appointment.CourseId
  }));
};

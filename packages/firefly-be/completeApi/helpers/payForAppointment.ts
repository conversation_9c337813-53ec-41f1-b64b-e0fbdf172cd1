import * as reservanto from "../../reservanto/reservantoOperations";

export const payForAppointment = async (
  appointmentId: number,
  amount: number,
  customerId: number,
  type: "replacement" | "credit" | "seasonPass",
  passUsageId?: number
) => {
  if (type === "replacement") {
    const res = await reservanto.payWithReplacement({
      appointmentId: Number(appointmentId),
      customerId: Number(customerId),
    });
    return res;
  } else if (type === "credit") {
    const res = await reservanto.payWithCredit({
      appointmentId: Number(appointmentId),
      customerId: Number(customerId),
      amount: Number(amount),
    });
    return res;
  } else if (type === "seasonPass") {
    const res = await reservanto.payWithViaPas({
      appointmentId: Number(appointmentId),
      customerId: Number(customerId),
      passUsageId: Number(passUsageId),
    });
    return res;
  }
};

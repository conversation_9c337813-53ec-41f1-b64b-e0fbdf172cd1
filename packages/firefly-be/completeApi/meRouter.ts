import express from "express";
import { isPasswordG<PERSON><PERSON>nough } from "../authentication/helpers";
import { UserRequest } from "../authentication/type";
import { RequestError } from "../errors/types";
import { UserService } from "../models/user/UserService";
import * as reservanto from "../reservanto/reservantoOperations";
import bcrypt from "bcrypt";

const router = express.Router();

// GET USER
router.get("/", async (req: UserRequest, res) => {
  let user = {
    role: "Guest",
  };

  if (req.user?.id) {
    const customerDetail = await reservanto.getCustomerDetails(
      Number(req.user.id)
    );
    const customerResponse = await reservanto.getCustomer(Number(req.user.id));
    if (customerResponse && customerDetail) {
      user = {
        ...customerResponse.Customer,
        ...customerDetail.CustomerDetails,
        role: "LoggedUser",
      };
    }
  }
  res.json(user);
});

// UPDATE USER
router.put("/", async (req: UserRequest, res, next) => {
  try {
    if (!req?.user?.id)
      return next(
        new RequestError({
          code: 400,
          message: "K požadované akci musíte být přihlášený",
        })
      );
    const { body } = req;
    const [firstName, ...lastName] = body.name.split(" ");

    if (body.password && !isPasswordGoodEnough(body.password)) {
      throw new RequestError({
        code: 400,
        message:
          "Heslo musí obsahovat alespoň 6 znaků, z toho jeden velký, jeden malý a jedno číslo.",
      });
    }

    const foundUser = await UserService.getUser({ email: body.email });
    if (req.user.email !== body.email && foundUser) {
      throw new RequestError({
        code: 400,
        message: "Tento uživatel již existuje",
      });
    }

    const response = await reservanto.updateCustomer(Number(req.user.id), {
      firstName,
      lastName: lastName.join(" "),
      ...body,
    });

    let userData: { email: string; passwordHash?: string } = {
      email: body.email,
    };
    if (body.password) {
      const passwordHash = bcrypt.hashSync(body.password, 8);
      userData = {
        ...userData,
        passwordHash,
      };
    }
    UserService.updateUser(userData, { userId: Number(req.user.id) });
    res.json(response.Customer);
  } catch (e) {
    next(e);
  }
});

// GET CREDIT TRANSACTIONS
router.get("/creditTransactions", async (req: UserRequest, res, next) => {
  if (req.user) {
    const customerDetail = await reservanto.getCustomerCreditTransactions({
      startDate: 1663493734,
      endDate: (new Date().getTime() + 100000000000) / 1000,
      customerId: Number(req.user.id),
    });
    res.json(customerDetail);
  } else {
    return next(
      new RequestError({
        code: 401,
        message: "K tomuto požadavku musíte být přihlášený",
      })
    );
  }
});

router.get("/replacements", async (req: UserRequest, res, next) => {
  try {
    if (!req?.user?.id) {
      throw new RequestError({
        code: 401,
        message: "You must be authorized for such a action",
      });
    }
    const reservations = await reservanto.getReplacements(Number(req.user.id));
    res.status(200).json(reservations.Items);
  } catch (e) {
    console.error(e);
    next(e);
  }
});

export default router;

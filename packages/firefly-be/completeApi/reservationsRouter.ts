import express from "express";
import { UserRequest } from "../authentication/type";
import { RequestError } from "../errors/types";
import * as reservanto from "../reservanto/reservantoOperations";
import { payForAppointment } from "./helpers/payForAppointment";
import { makeEventsFromAppointments } from "./helpers/makeEventsFromAppointments";
import * as R from "rambda";
import { Appointment } from "../reservanto/types";

const router = express.Router();

router.get("/", async (req: UserRequest, res, next) => {
  const customerId = req.user?.id;
  try {
    const eventsResponse = await reservanto.getUserEvents({
      startDate: 1663493734,
      endDate: (new Date().getTime() + 100000000000) / 1000,
      customerId: Number(customerId),
    });

    const alternateAppointmentsResponse =
      await reservanto.getCustomerAlternateAppointments({
        startDate: 1663493734,
        endDate: (new Date().getTime() + 100000000000) / 1000,
        customerId: Number(customerId),
      });

    const alternateCursesAppointmentsResponse =
      await reservanto.getCustomerAlternateCourses({
        startDate: 1663493734,
        endDate: (new Date().getTime() + 100000000000) / 1000,
        customerId: Number(customerId),
      });

    const alternateCoursesAppointments: Appointment[] = R.flatten(
      alternateCursesAppointmentsResponse.Items.map((course) =>
        course.Appointments.map((a) => ({
          ...a,
          Price: course.PriceWithVat / course.Appointments.length,
        }))
      )
    );

    const futureCoursesAppointments = alternateCoursesAppointments.filter(
      (alternate) => alternate.StartDate >= new Date().getTime() / 1000
    );

    const events = [
      ...eventsResponse.Events.map((event) => ({ ...event, status: "booked" })),
      ...alternateAppointmentsResponse.Items.map((item) => ({
        ...item.AlternateEvent,
        Status: "waiting",
      })),
      ...makeEventsFromAppointments(futureCoursesAppointments),
    ];

    res.json(events);
  } catch (e) {
    console.error(e);
    return next(new RequestError({ code: 400, message: "Něco se pokazilo" }));
  }
});

router.put("/:appointmentId/pay", async (req: UserRequest, res, next) => {
  try {
    const { appointmentId } = req.params;
    const type = req.query.type as "credit" | "replacement" | "seasonPass";
    const amount = Number(req.query.amount);
    const customerId = Number(req.user?.id);
    const passUsageId = req.query.passUsageId
      ? Number(req.query.passUsageId)
      : undefined;

    const eventsResponse = await reservanto.getUserEvents({
      startDate: 1663493734,
      endDate: (new Date().getTime() + 100000000000) / 1000,
      customerId: Number(customerId),
    });

    let events = eventsResponse.Events;

    const certainEvent = events.find(
      (a) => Number(appointmentId) === a.AppointmentId
    );

    events = certainEvent?.CourseId
      ? events.filter(
          (a) =>
            a.CourseId === certainEvent.CourseId &&
            a.BookingServiceId === certainEvent.BookingServiceId
        )
      : [certainEvent!];

    if (certainEvent?.CourseId && events.length > 1) {
      // const lesson = lessonResponse?.data?.[0]?.attributes;
      let moneyLeft = amount;
      for await (const a of events) {
        if (moneyLeft > 0) {
          try {
            const res = await payForAppointment(
              Number(a.AppointmentId),
              moneyLeft > a.Price ? a.Price : moneyLeft,
              customerId,
              type,
              passUsageId
            );
            // if reservation isn't payed yet
            if (!res?.IsError) {
              if ((res?.UsedCreditAmount || 0) < a.Price) {
                moneyLeft += res?.UsedCreditAmount || 0;
              }
              moneyLeft -= a.Price;
            }
          } catch (e) {
            throw e;
          }
        }
      }
    } else {
      await payForAppointment(
        Number(appointmentId),
        amount,
        customerId,
        type,
        passUsageId
      );
    }
    res.status(200).send();
  } catch (e) {
    console.error(e);
    next(e);
  }
});

export default router;

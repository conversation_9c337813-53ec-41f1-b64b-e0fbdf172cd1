import express from "express";
import { uniq } from "rambda";
import { getTimeInUnixTimeStamp } from "../helpers";
import * as strapi from "../strapi/strapiOperations";
import * as reservanto from "../reservanto/reservantoOperations";
import { CourseDetail } from "./types";
import { GetCourseResponse } from "../reservanto/types";
import { UserRequest } from "../authentication/type";
import { RequestError } from "../errors/types";

const router = express.Router();

router.get("/:courseId/customerEvents", async (req: UserRequest, res, next) => {
  const { courseId } = req.params;
  const { user } = req;
  const customerId = user?.id || req.query.customerId;

  try {
    const courseEvents = await reservanto.getCourseCustomerEvents({
      courseId: Number(courseId),
      customerId: Number(customerId),
    });

    res.json(courseEvents.Result);
  } catch (e) {
    next(e);
  }
});

router.get("/:appointmentId", async (req, res, next) => {
  try {
    const { appointmentId } = req.params;

    const appointmentRes = await reservanto.getClass({
      appointmentId: Number(appointmentId),
    });
    const appointment = appointmentRes.Result;

    let appointments = [];
    let course: GetCourseResponse | null = null;

    if (appointment.CourseId) {
      course =
        (await reservanto.getCourse({
          courseId: appointment.CourseId,
        })) || null;
      appointments = course.Result.Appointments.sort(
        (a, b) => a.StartDate - b.StartDate
      );
    } else {
      appointments.push(appointment);
    }

    const lessonResponse = await strapi.getLessonsByBookingServiceSpecificPopulate(
      String(appointment.BookingServiceId),
    );

    const lesson = lessonResponse?.data?.[0]?.attributes;

    const fromNow = getTimeInUnixTimeStamp(new Date());
    const appointmentsLeft = appointments.filter((a) => a.StartDate > fromNow);

    const lessonDates = uniq(
      appointmentsLeft.map((a) => new Date(a.StartDate * 1000))
    );

    const foundLector = lesson?.activity?.data?.attributes.lectors?.data?.find(l => l.attributes.name === appointment.BookingResourceName);


    const freeSpaces = course
      ? (course.Result.Capacity || 0) - (course.Result.OccupiedCapacity || 0)
      : appointment.Capacity - appointment.OccupiedCapacity;

    const detailData: CourseDetail = {
      bookingServiceId: appointment.BookingServiceId,
      totalPrice: (lesson?.price || 0) * appointmentsLeft.length,
      image: lesson?.activity?.data?.attributes?.imagePreview.data?.attributes.url,
      totalLessons: appointmentsLeft.length,
      courseId: course?.Result.Id || null,
      locationName: appointment.CalendarName,
      locationId: appointment.CalendarId,
      title: lesson?.name,
      description: lesson?.description,
      lectorName: appointment.BookingResourceName,
      lectorId: appointment.BookingResourceId,
      lectorImage: foundLector?.attributes.image?.data?.attributes?.url,
      coloredImage: foundLector?.attributes.coloredImage?.data?.attributes?.url,
      freeSpaces,
      fromDate: appointments[0].StartDate,
      toDate: appointments[appointments.length - 1].StartDate,
      lessonDates: lessonDates,
    };
    res.json(detailData);
  } catch (e) {
    console.error(e);
    return next(
      new RequestError({
        code: 400,
        message: "Něco se pokazilo",
      })
    );
  }
});

export default router;

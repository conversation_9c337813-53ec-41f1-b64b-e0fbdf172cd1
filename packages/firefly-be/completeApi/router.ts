import express from "express";
import "../cronJobs";
import bookingRouter from "./bookingRouter";
import classesRouter from "./classesRouter";
import courseRouter from "./courseRouter";
import meRouter from "./meRouter";
import reservationsRouter from "./reservationsRouter";

const router = express.Router();

router.use("/classes", classesRouter);
router.use("/me", meRouter);
router.use("/reservations", reservationsRouter);
router.use("/booking", bookingRouter);
router.use("/courses", courseRouter);



export default router;

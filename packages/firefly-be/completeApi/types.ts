export interface GetAppointmentsInput {
  locationId: number;
  activityId: number | null;
  isForBeginners: boolean;
  lessonTypeId: number;
}

export interface CourseDetail {
  totalPrice: number;
  totalLessons: number;
  locationName: string;
  courseId: number | null;
  locationId: number;
  title?: string;
  description?: string;
  bookingServiceId: number;
  lectorName: string;
  lectorId: number;
  freeSpaces: number;
  fromDate: number;
  toDate: number;
  image?: string;
  lessonDates: Date[];
  lectorImage?: string;
  coloredImage?: string;
}

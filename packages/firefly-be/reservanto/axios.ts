import axios from "axios";
import { RequestError } from "../errors/types";
import { getShortTimeToken } from "./getToken";
import { ReservantoAppVersion, ReservantoAppVersionEnum } from "./versions";

export const reservantoApi = axios.create({
  baseURL: process.env.RESERVANTO_API_URL,
  maxContentLength: 100000000
});
reservantoApi.defaults.headers.common = {
  "Content-Type": "application/json;charset=UTF-8",
};

let shortTimeToken: string | null = null;

reservantoApi.interceptors.request.use(async (request) => {
  const apiVersion =
    ReservantoAppVersion[
      (request.data?.apiVersion as ReservantoAppVersionEnum) ||
        ReservantoAppVersionEnum.v1
    ];

  if (
    shortTimeToken == null &&
    request.url !== "/Authorize/GetShortTimeToken"
  ) {
    shortTimeToken = await getShortTimeToken();
    if (!shortTimeToken) {
      throw new Error("Cannot obtain refresh token");
    }
  }

  let newRequest = {
    ...request,
    headers: shortTimeToken
      ? {
          ...request.headers,
          Authorization: shortTimeToken,
        }
      : request.headers,
    url: request.url?.includes(apiVersion)
      ? request.url
      : `/${apiVersion}${request.url}`,
  };

  // interceptor sometimes receive data as a string - https://github.com/axios/axios/issues/3986
  if (typeof newRequest.data === "object") {
    newRequest = {
      ...newRequest,
      data: {
        ...newRequest.data,
        TimeStamp: Math.ceil(new Date().getTime() / 1000),
      },
    };
  }

  console.log(
    `Request: ${request.method?.toUpperCase()} ${request.baseURL}${
      request.url
    }`,
    "DATA: ",
    request.data,
    "PARAMS: ",
    request.params
  );
  return newRequest;
});

reservantoApi.interceptors.response.use(
  (response) => {
    if (
      response.data.IsError &&
      response.data.ErrorMessage !== "Rezervace je již plně uhrazena."
    ) {
      console.error(
        response.request.url,
        response.request.data,
        response.request.params
      );
      console.error(response.data);
      throw new RequestError({
        code: 400,
        message: response.data.ErrorMessage,
      });
    }
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    if (error?.response?.status === 401) {
      try {
        shortTimeToken = null;
        shortTimeToken = await getShortTimeToken();
        if (
          shortTimeToken &&
          originalRequest.url !== "/Authorize/GetShortTimeToken"
        ) {
          originalRequest._retry = true;
          originalRequest.headers["Authorization"] = shortTimeToken;
          return reservantoApi(originalRequest);
        }
        return Promise.reject(new Error("Error obtaining shortTimeToken"));
      } catch (e) {
        console.log(e);
        return Promise.reject(e);
      }
    }
    return Promise.reject(error);
  }
);

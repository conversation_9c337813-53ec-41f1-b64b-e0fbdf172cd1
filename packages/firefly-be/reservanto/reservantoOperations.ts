import { reservanto<PERSON><PERSON> } from "./axios";
import {
  CreditTransactionsResponse,
  CustomerDetailsResponse,
  CustomerResponse,
  EventGetListResponse,
  GetAvailableAppointments,
  GetBookingServicesResponse,
  GetClassResponse,
  GetCourseResponse,
  GetCustomerAlternateAppointments,
  GetCustomerAlternateCourses,
  PayResponse,
  ReplacementsResponse,
} from "./types";

export const getBookingServices =
  async (): Promise<GetBookingServicesResponse> => {
    const response = await reservantoApi({
      method: "post",
      url: "/BookingService/GetList",
      // We need to add empty object here so interceptor adds correct default params into request
      data: {},
    });
    return response?.data;
  };

export const getAvailableAppointments = async (
  SegmentId: number,
  IntervalStart: number,
  IntervalEnd: number
): Promise<GetAvailableAppointments> => {
  const response = await reservantoApi({
    method: "post",
    url: "/Classes/GetAvailableAppointments",
    data: {
      LocationId: 18178,
      SegmentId,
      IntervalStart,
      IntervalEnd,
    },
  });
  return response?.data;
};

interface CreateCustomerInput {
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
}

export const createCustomer = async (
  input: CreateCustomerInput
): Promise<CustomerResponse> => {
  const response = await reservantoApi({
    method: "post",
    url: "/Customer/Create",
    data: {
      FirstName: input.firstName,
      LastName: input.lastName,
      Email: input.email,
      Phone: input.phone,
      Gender: "NotKnown",
    },
  });
  return response?.data;
};

export const updateCustomer = async (
  customerId: number,
  input: CreateCustomerInput
): Promise<CustomerResponse> => {
  const response = await reservantoApi({
    method: "post",
    url: "/Customer/Edit",
    data: {
      Id: customerId,
      CustomerId: customerId,
      FirstName: input.firstName,
      LastName: input.lastName,
      Email: input.email,
      Phone: input.phone,
      Gender: "NotKnown",
    },
  });
  return response?.data;
};

export const getCustomer = async (
  customerId: number
): Promise<CustomerResponse> => {
  const response = await reservantoApi({
    method: "post",
    url: "/Customer/Get",
    data: {
      CustomerId: customerId,
    },
  });
  return response?.data;
};

export const getCustomerDetails = async (
  customerId: number
): Promise<CustomerDetailsResponse> => {
  const response = await reservantoApi({
    method: "post",
    url: "/Customer/Details",
    data: {
      CustomerId: customerId,
    },
  });
  return response?.data;
};

interface GetCustomerCreditTransactionsParams {
  customerId: number;
  startDate: number;
  endDate: number;
}
export const getCustomerCreditTransactions = async ({
  customerId,
  startDate,
  endDate,
}: GetCustomerCreditTransactionsParams): Promise<CreditTransactionsResponse> => {
  const response = await reservantoApi({
    method: "post",
    url: "/Customer/CreditTransactions",
    data: {
      CustomerId: customerId,
      StartDate: Math.floor(startDate),
      EndDate: Math.floor(endDate),
    },
  });
  return response?.data;
};

export const getUserEvents = async ({
  startDate,
  endDate,
  customerId,
}: {
  startDate: number;
  endDate: number;
  customerId: number;
}): Promise<EventGetListResponse> => {
  const response = await reservantoApi({
    method: "post",
    url: "/Event/GetListByCustomer",
    data: {
      StartDate: Math.floor(startDate),
      EndDate: Math.floor(endDate),
      CustomerId: customerId,
    },
  });

  return response?.data;
};

export const getReplacements = async (
  CustomerId: number
): Promise<ReplacementsResponse> => {
  const response = await reservantoApi({
    method: "post",
    url: "/ServiceSubstitution/GetForCustomer",
    data: {
      CustomerId,
    },
  });

  return response?.data;
};

export const createBooking = async ({
  appointmentId,
  customerNote,
  customerId,
}: {
  appointmentId: number;
  customerNote: string;
  customerId: number;
}): Promise<EventGetListResponse | null> => {
  try {
    const response = await reservantoApi({
      method: "post",
      url: "/Classes/CreateBooking",
      data: {
        AppointmentId: appointmentId,
        CustomerId: customerId,
        CustomerNote: customerNote,
      },
    });
    return response?.data;
  } catch (e) {
    console.log(e);
    return null;
  }
};

export const cancelBooking = async ({
  appointmentId,
  customerId,
  enableCancelWholeCourse,
}: {
  appointmentId: number;
  customerId: number;
  enableCancelWholeCourse: boolean;
}): Promise<void> => {
  await reservantoApi({
    method: "post",
    url: "/Booking/Cancel",
    data: {
      AppointmentId: appointmentId,
      CustomerId: customerId,
      EnableCancelWholeCourse: enableCancelWholeCourse,
    },
  });
  return;
};

export const payWithReplacement = async ({
  appointmentId,
  customerId,
}: {
  appointmentId: number;
  customerId: number;
}): Promise<PayResponse> => {
  const res = await reservantoApi({
    method: "post",
    url: "/Booking/AddPaymentViaServiceSubstitution",
    data: {
      AppointmentId: appointmentId,
      CustomerId: customerId,
    },
  });
  return res.data;
};

export const payWithCredit = async ({
  appointmentId,
  customerId,
  amount,
}: {
  appointmentId: number;
  customerId: number;
  amount: number;
}): Promise<PayResponse> => {
  const res = await reservantoApi({
    method: "post",
    url: "/Booking/AddPaymentViaCredit",
    data: {
      AppointmentId: appointmentId,
      CustomerId: customerId,
      Amount: amount,
    },
  });
  return res.data;
};

export const payWithViaPas = async ({
  appointmentId,
  customerId,
  passUsageId,
}: {
  appointmentId: number;
  customerId: number;
  passUsageId: number;
}): Promise<PayResponse> => {
  const res = await reservantoApi({
    method: "post",
    url: "/Booking/AddPaymentViaPass",
    data: {
      AppointmentId: appointmentId,
      CustomerId: customerId,
      PassUsageId: passUsageId,
    },
  });
  return res.data;
};

export const getCourse = async ({
  courseId,
}: {
  courseId: number;
}): Promise<GetCourseResponse> => {
  const res = await reservantoApi({
    method: "post",
    url: "/Course/Get",
    data: {
      CourseId: courseId,
    },
  });
  return res.data;
};

export const getClass = async ({
  appointmentId,
}: {
  appointmentId: number;
}): Promise<GetClassResponse> => {
  const res = await reservantoApi({
    method: "post",
    url: "/Classes/Get",
    data: {
      AppointmentId: appointmentId,
    },
  });
  return res.data;
};

export const getCourseCustomerEvents = async ({
  courseId,
  customerId,
}: {
  courseId: number;
  customerId: number;
}): Promise<GetClassResponse> => {
  const res = await reservantoApi({
    method: "post",
    url: "/Course/GetCustomerEvents",
    data: {
      CourseId: courseId,
      CustomerId: customerId,
    },
  });
  return res.data;
};

export const getCustomerAlternateAppointments = async ({
  startDate,
  endDate,
  customerId,
}: {
  startDate: number;
  endDate: number;
  customerId: number;
}): Promise<GetCustomerAlternateAppointments> => {
  const res = await reservantoApi({
    method: "post",
    url: "/Alternates/GetAppointmentsSubscribedByCustomer",
    data: {
      CustomerId: customerId,
      StartDate: startDate,
      EndDate: endDate,
    },
  });
  return res.data;
};

export const getCustomerAlternateCourses = async ({
  startDate,
  endDate,
  customerId,
}: {
  startDate: number;
  endDate: number;
  customerId: number;
}): Promise<GetCustomerAlternateCourses> => {
  const res = await reservantoApi({
    method: "post",
    url: "/Alternates/GetCoursesSubscribedByCustomer",
    data: {
      CustomerId: customerId,
      StartDate: startDate,
      EndDate: endDate,
    },
  });
  return res.data;
};

export const addToAlternateAppointment = async ({
  appointmentId,
  customerId,
}: {
  appointmentId: number;
  customerId: number;
}): Promise<void> => {
  await reservantoApi({
    method: "post",
    url: "/Alternates/AddToAppointment",
    data: {
      CustomerId: customerId,
      AppointmentId: appointmentId,
    },
  });
  return;
};

export const removeFromAlternateAppointment = async ({
  appointmentId,
  customerId,
}: {
  appointmentId: number;
  customerId: number;
}): Promise<void> => {
  await reservantoApi({
    method: "post",
    url: "/Alternates/RemoveFromAppointment",
    data: {
      CustomerId: customerId,
      AppointmentId: appointmentId,
    },
  });
  return;
};

export const addToAlternateCourse = async ({
  courseId,
  customerId,
}: {
  courseId: number;
  customerId: number;
}): Promise<void> => {
  await reservantoApi({
    method: "post",
    url: "/Alternates/AddToCourse",
    data: {
      CustomerId: customerId,
      CourseId: courseId,
    },
  });
  return;
};

export const removeFromAlternateCourse = async ({
  courseId,
  customerId,
}: {
  courseId: number;
  customerId: number;
}): Promise<void> => {
  await reservantoApi({
    method: "post",
    url: "/Alternates/RemoveFromCourse",
    data: {
      CustomerId: customerId,
      CourseId: courseId,
    },
  });
  return;
};

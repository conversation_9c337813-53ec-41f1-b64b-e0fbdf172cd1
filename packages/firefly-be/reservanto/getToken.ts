import { reservanto<PERSON><PERSON> } from "./axios";
import { GetShortTimeTokenResponse } from "./types";

export const getShortTimeToken = async (): Promise<string | null> => {
  try {
  const response = await reservanto<PERSON><PERSON>({
    method: "post",
    url: "/Authorize/GetShortTimeToken",
    data: {
      LongTimeToken: process.env.RESERVANTO_LONG_TIME_TOKEN,
    },
  });
  const data: GetShortTimeTokenResponse = response?.data;
  if (data?.IsError && data.ErrorMessage) {
    throw new Error(data.ErrorMessage);
  }

  return data?.ShortTimeToken;
} catch (e) {
  console.log(e);
  return null;
}
};

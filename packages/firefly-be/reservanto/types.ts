export interface ReservantoErrors {
  IsError: boolean;
  ErrorParameter?: string | null;
  ErrorMessage?: string | null;
  ErrorParameters?: string[] | null;
  ErrorMessages?: string[] | null;
}

export interface GetShortTimeTokenResponse extends ReservantoErrors {
  ShortTimeToken: string | null;
}

export interface PayResponse extends ReservantoErrors {
  UsedCreditAmount: number;
}

export interface Course {
  Id: number;
  Name: string;
  StartDate: number;
  EndDate: number;
  PriceWithVat: number;
  Capacity: number;
  OccupiedCapacity: number;
  BookingServiceName: string;
  BookingServiceId: number;
  BookingResourceName: string;
  BookingResourceId: number;
  LocationId: number | null;
  Appointments: Appointment[];
}

export interface GetCourseResponse extends ReservantoErrors {
  Result: Course;
}

export interface GetClassResponse extends ReservantoErrors {
  Result: Appointment;
}

export interface BookingService {
  Id: number;
  Name: string;
  Description: string;
  Duration: number;
  Price: number;
  SegmentId: string;
  BookingResourceIds: number[];
}

export interface Appointment {
  Id: number;
  StartDate: number;
  EndDate: number;
  BookingResourceId: number;
  BookingResourceName: string;
  BookingServiceId: number;
  BookingServiceName: string;
  CalendarId: number;
  CalendarName: string;
  CourseId: number | null;
  IsAvailable: boolean;
  Capacity: number;
  OccupiedCapacity: number;
  Price?: number;
  FormattedAvailability: string;
}

export interface GetBookingServicesResponse extends ReservantoErrors {
  BookingServices: BookingService[];
}

export interface GetAvailableAppointments extends ReservantoErrors {
  Items: Appointment[];
}

export interface Customer {
  Id: number;
  CreatedAt: number;
  Name: string;
  Phone: string;
  Email: string;
  Deleted: boolean;
}

export interface Usage {
  BookingServiceId: number;
  Count: number;
  Used: number;
  PassUsageId: number;
}

interface BoughtPass {
  ActiveFrom: number;
  AggregatedCount: number | null;
  CreatedAt: number;
  DurationMinutes: number;
  ExpirationDate: number;
  IsIntermissionActive: boolean;
  PassId: number;
  PassName: string;
  PassToCustomerId: number;
  Usages: Usage[];
}

export interface CustomerDetails {
  Id: number;
  PriceLevelId: number;
  PriceLevelName: string;
  LoyaltyPointsCanBeUsed: boolean;
  LoyaltyPointsCash: string;
  Credit: string;
  LoyaltyPoints: string;
  BoughtPasses?: BoughtPass[];
}

export interface AlternateEvent {
  AppointmentId: number;
  StartDate: number;
  BookingServiceId: number;
  EndDate: number;
  ServiceName: string;
  CourseId?: number | null;
  CalendarId: number;
  CalendarName: string;
  Status: "waiting"
}

export interface Event {
  AppointmentId: number;
  StartDate: number;
  BookingServiceId: number;
  EndDate: number;
  PaymentMethodFormatted: string | null;
  MerchantNote: string;
  LocationName: string;
  SourceName: string;
  SourceId: number;
  ServiceName: string;
  SegmentName: string;
  CustomerId: number;
  CustomerFullName: string;
  CourseId?: number | null;
  CustomerNote: string;
  Price: number;
  PaidPartOfPrice: number;
  CalendarId: number;
  CalendarName: string;
  IsPaid: number;
  Count: number;
  NotShowStatus: number;
  Status: "booked"
}

export interface Transaction {
  CreatedAt: number;
  Value: number;
  Note: number;
  Id: number;
}

export interface Replacement {
  Id: number;
  CreatedAt: number;
  CustomerId: number;
  SourceBookingServiceId: number;
  SourceBookingServiceName: number;
  ExpirationDate: number | null;
  Services: { Id: number; Name: string }[];
}
export interface CreditTransactionsResponse extends ReservantoErrors {
  Customer: string;
  CurrentCredit: number;
  Transactions: Transaction[];
}

export interface EventGetListResponse extends ReservantoErrors {
  Events: Event[];
}

export interface CustomerResponse extends ReservantoErrors {
  Customer: Customer;
}

export interface CustomerDetailsResponse extends ReservantoErrors {
  CustomerDetails: CustomerDetails;
}

export interface ReplacementsResponse extends ReservantoErrors {
  Items: Replacement;
}

export interface GetCustomerAlternateCourses extends ReservantoErrors {
  Items: Course[];
}

interface AlternateAppointment extends Appointment {
  AlternateEvent: Event;
}

export interface GetCustomerAlternateAppointments extends ReservantoErrors {
  Items: AlternateAppointment[];
}

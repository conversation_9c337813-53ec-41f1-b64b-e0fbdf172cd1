import { mapReservantoLessonToStrapiLesson } from "../../strapi/helpers";
import {
  deleteLesson,
  getLessons,
  insertLessons,
  updateLesson,
} from "../../strapi/strapiOperations";
import { getBookingServices } from "../reservantoOperations";

export const updateLessonsFromReservantoToStrapi = async () => {
  try {
    const reservantoLessons = await getBookingServices();
    const strapiLessons = await getLessons({});

    await Promise.all(
      reservantoLessons.BookingServices.map(async (bookingService) => {
        const foundLessonOnStrapi = strapiLessons?.data?.find(
          (lesson) => lesson.attributes.bookingServiceId === bookingService.Id
        );
        if (foundLessonOnStrapi) {
          return updateLesson(
            foundLessonOnStrapi.id,
            mapReservantoLessonToStrapiLesson(bookingService)
          );
        }
        return insertLessons(
          mapReservantoLessonToStrapiLesson(bookingService)
        );
      })
    );
    const reservantoLessonIds = reservantoLessons.BookingServices.map(
      (service) => service.Id
    );
    await Promise.all(
      (strapiLessons.data || []).map((lesson) => {
        if (!reservantoLessonIds.includes(lesson.attributes.bookingServiceId)) {
          return deleteLesson(lesson.id);
        }
        return;
      })
    );
  } catch (e) {
    console.error(e);
  }
};

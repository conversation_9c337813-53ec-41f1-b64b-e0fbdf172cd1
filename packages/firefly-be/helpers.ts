import { CustomerDetails } from "./reservanto/types";

export const getTimeInUnixTimeStamp = (date: Date) => {
  return date.getTime() / 1000;
};

export const getTimeFromUnixTimeStamp = (date: number) => {
  return new Date(date * 1000);
};

export const getVS = (phone?: string) => {
  phone = phone || "";
  return phone.replace(/\s/g, '').substring(phone.length - 9, phone.length);
};

export const availablePastoPay = (bookingServiceId: number, me: CustomerDetails) => {
  let availablePass: null | number = null;
  me.BoughtPasses?.forEach((pass) =>
    pass.Usages.forEach((usage) => {
      if (usage.BookingServiceId === bookingServiceId) {
        availablePass = usage.PassUsageId;
      }
    })
  );
  return availablePass;
};

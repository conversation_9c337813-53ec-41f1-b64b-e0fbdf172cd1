import User from "./UserModel";

interface UserInput {
  email?: string;
  id?: string;
  resetPasswordToken?: string;
}

export const UserService = {
  getUser: async (userInput: UserInput) => {
    const user = await User.query().where(userInput).first();
    return user;
  },

  createUser: async (user: {
    email: string;
    passwordHash: string;
    id: string;
  }) => {
    return User.query().insert(user);
  },

  updateUser: async (
    user: {
      email?: string;
      passwordHash?: string;
      resetPasswordToken?: string | null;
    },
    findBy: {
      userId?: number;
      email?: string;
      resetPasswordToken?: string;
    }
  ) => {
    if (findBy?.userId) {
      return User.query().patch(user).findById(findBy.userId);
    }
    return User.query().patch(user).where(findBy);
  },
};

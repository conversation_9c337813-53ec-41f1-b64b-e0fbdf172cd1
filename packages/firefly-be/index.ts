import express from "express";
import cookiesParser from "cookie-parser";
require("dotenv").config({ path: `${__dirname}/.env` });

import authMiddleware from "./authentication/authMiddleware";
import { errorMiddleware } from "./errors/errorMiddleware";
import cacheMiddleware from "./cacheMiddleware";
import completeApiRouter from "./completeApi/router";
import importRouter from "./importRouter";
import { dbInit } from "./db/db";
import { redisClient } from "./initRedis";

const app = express();
app.use(cookiesParser());

app.use(express.json());
app.use(express.urlencoded({ extended: false }));
app.use("/", importRouter);
app.use(authMiddleware);
app.use("/", cacheMiddleware, completeApiRouter);
app.use(errorMiddleware);

app.listen(process.env.PORT, async () => {
  await redisClient.connect();
  const knex = dbInit();
  // await knex.migrate.latest();
  console.log(`Server is running on port ${process.env.PORT}`);
});

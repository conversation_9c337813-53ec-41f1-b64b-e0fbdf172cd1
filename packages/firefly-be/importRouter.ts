import express from "express";
// import * as reservanto from "./reservanto/reservantoOperations";
// import { UserService } from "./models/user/UserService";

// import { users } from "./users";

const router = express.Router();

// const timeout = (ms: number) => {
//   return new Promise((resolve) => setTimeout(resolve, ms));
// };

router.get("/importUsers", async (_, res) => {
  let migratedUsersAmount = 0;

  // for (const user of users) {
  //   try {
  //     const [firstName, ...lastName] = user.display_name.split(" ");
  //     const response = await reservanto.createCustomer({
  //       firstName,
  //       lastName: lastName.join(" "),
  //       email: user.user_email,
  //       phone: user.phone_number?.toString() || "",
  //     });

  //     await UserService.createUser({
  //       email: user.user_email,
  //       passwordHash: user.user_pass,
  //       id: response.Customer.Id.toString(),
  //     });
  //     await timeout(300);
  //     migratedUsersAmount += 1;
  //   } catch (error) {
  //     console.error(error);
  //   }
  // }

  res.json({
    migratedUsersAmount,
  });
});

export default router;

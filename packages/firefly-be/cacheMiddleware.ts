import express from "express";
import { UserRequest } from "./authentication/type";
import { redisClient } from "./initRedis";

const sortObjectKeysAlphabetically = (obj: Object) => {
  return Object.keys(obj).sort();
};

const router = express.Router();

router.use(async (req: UserRequest, res, next) => {
  if (
    req.method !== "GET" ||
    req.path.includes("/me") ||
    req.path.includes("/reservations") ||
    req.path.includes('/customerEvents')
  ) {
    return next();
  }

  const alphabeticallySortedParams = sortObjectKeysAlphabetically(req.params);
  const alphabeticallySortedQuery = sortObjectKeysAlphabetically(req.query);

  let redisStorageKey: string = `path:${req.path};`;

  alphabeticallySortedParams.forEach((key) => {
    const paramValue = req.params[key];
    redisStorageKey += `${key}:${paramValue};`;
  });

  alphabeticallySortedQuery.forEach((key) => {
    let queryValue = req.query[key];
    if (key === "from" || key === "to") {
      const time = new Date(Number(queryValue) * 1000);
      time.setMinutes(0);
      time.setSeconds(0);
      time.setMilliseconds(0);
      queryValue = (time.getTime() / 1000).toString();
    }
    redisStorageKey += `${key}:${queryValue};`;
  });

  const cachedResponse = await redisClient.get(redisStorageKey);

  let wasResponseAlreadySent = false;
  if (cachedResponse) {
    try {
      const objectData = JSON.parse(cachedResponse);
      wasResponseAlreadySent = true;
      console.log("Returning cached");
      res.send(objectData);
    } catch (e) {
      console.log(e);
    }
  }

  let responseData: any = null;

  const oldJson = res.json;
  (res as any).json = function (data: any) {
    responseData = data;
    const stringifiedData = JSON.stringify(responseData);
    redisClient.set(redisStorageKey, stringifiedData, {
      EX: 60 * 60,
    });
    if (res.finished || cachedResponse) {
      return;
    }
    console.log("Returning original");
    return oldJson.apply(res, arguments as any);
  };

  const oldSend = res.send;
  (res as any).send = function (data: any) {
    responseData = data;
    const stringifiedData = JSON.stringify(responseData);
    redisClient.set(redisStorageKey, stringifiedData, {
      EX: 60 * 60,
    });
    if (res.finished || cachedResponse) {
      return;
    }
    console.log("Returning original");
    return oldSend.apply(res, arguments as any);
  };

  next();
});

export default router;

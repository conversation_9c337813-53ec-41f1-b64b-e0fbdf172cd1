import axios from "axios";

export const strapiApi = axios.create({
  baseURL: process.env.STRAPI_API_URL,
  maxContentLength: 100000000000
});

strapiApi.interceptors.request.use(async (request) => {
  console.log(
    `Request: ${request.method?.toUpperCase()} ${request.baseURL}${
      request.url
    }`,
    "DATA: ",
    request.data,
    "PARAMS: ",
    request.params
  );
  return request;
});

export interface StrapiArrayResponse<T> {
  data?: {
    id: number;
    attributes: T;
  }[];
  meta?: any;
}

export interface StrapiResponse<T> {
  data: {
    id: number;
    attributes: T;
  };
  meta: any;
}

export interface Lesson {
  id?: string;
  description: string;
  name: string;
  price: number;
  duration: number;
  bookingServiceId: number;
  activities?: StrapiArrayResponse<Activity>;
  activity?: StrapiResponse<Activity>;
  // ...
}

export interface Activity {
  id: string;
  title: string;
  slug: string;
  lessons: StrapiArrayResponse<Lesson>;
  lectors: StrapiArrayResponse<Lector>;
  imagePreview: StrapiResponse<{
    alternativeText: string;
    url: string;
  }>;
}

export interface Lector {
  id: string;
  name: string;
  slug: string;
  image: StrapiResponse<{
    alternativeText: string;
    url: string;
  }>;
  coloredImage: StrapiResponse<{
    alternativeText: string;
    url: string;
  }>;
}

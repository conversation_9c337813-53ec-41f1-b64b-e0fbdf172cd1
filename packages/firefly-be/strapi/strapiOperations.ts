import { strapiApi } from "./axios";
import { Activity, Lesson, StrapiArrayResponse } from "./types";

interface GetLessonParams {
  activityIds?: string[];
  lessonTypeIds?: string[];
  isForBeginners?: string;
}

export const getLessons = async ({
  activityIds,
  lessonTypeIds,
  isForBeginners,
}: GetLessonParams): Promise<StrapiArrayResponse<Lesson>> => {

  const response = await strapiApi({
    method: "get",
    url: "/api/lessons",
    params: {
      "pagination[pageSize]": 300,
      "filters[activities][id][$in]": activityIds,
      "filters[lesson_type][id][$in]": lessonTypeIds,
      "filters[isForBeginners][$eq]": isForBeginners,
    },
  });
  return response?.data;
};

export const getActivities = async (
  ids: string[]
): Promise<StrapiArrayResponse<Activity>> => {
  const response = await strapi<PERSON><PERSON>({
    method: "get",
    url: "/api/activities",
    params: {
      "pagination[pageSize]": 100,
      "filters[id][$in]": ids,
    },
  });
  return response?.data;
};

export const getLessonsByBookingService = async (
  bookingServiceId: string,
  deep?: number
): Promise<StrapiArrayResponse<Lesson>> => {
  let params: any = {
    "filters[bookingServiceId][$eq]": bookingServiceId,
  };
  if (deep) {
    params = {
      ...params,
      populate: `deep,${deep}`,
    };
  }

  const response = await strapiApi({
    method: "get",
    url: "/api/lessons",
    params,
  });
  return response?.data;
};

export const getLessonsByBookingServiceSpecificPopulate = async (
  bookingServiceId: string,
): Promise<StrapiArrayResponse<Lesson>> => {
  let params = {
    "filters[bookingServiceId][$eq]": bookingServiceId,
    'populate[0]': 'activity.imagePreview',
    'populate[1]': 'activity.lectors.image',
    'populate[2]': 'activity.lectors.coloredImage'
  };

  const response = await strapiApi({
    method: "get",
    url: "/api/lessons",
    params,
  });
  return response?.data;
};

export const updateLesson = (strapiLessonId: number, data: Lesson) =>
  strapiApi({
    method: "put",
    url: `/api/lessons/${strapiLessonId}`,
    data: {
      data,
    },
  });

export const deleteLesson = (strapiLessonId: number) =>
  strapiApi({
    method: "delete",
    url: `/api/lessons/${strapiLessonId}`,
  });

export const insertLessons = (data: Lesson) =>
  strapiApi({
    method: "post",
    url: "/api/lessons",
    data: {
      data,
    },
  });

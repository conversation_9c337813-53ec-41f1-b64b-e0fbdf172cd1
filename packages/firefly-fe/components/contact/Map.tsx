import { LocationOn } from "@mui/icons-material";
import { IconButton } from "@mui/material";
import React, { useEffect, useRef } from "react";
import MapboxMap, { MapRef, Marker } from "react-map-gl";
import { useIsMobile } from "../../helpers/device";
import { isSSR } from "../../helpers/image";
import Link from "../Link";

interface Props {
  coordinate?:
    | ({ latitude: number | null; longitude: number | null } | null | undefined)
    | null;
  style?: React.CSSProperties;
  url?: string;
}

export const Map = ({ coordinate, style, url }: Props) => {
  const ref = useRef<MapRef>(null);
  const isMobile = useIsMobile()

  useEffect(() => {
    setTimeout(() => ref.current?.resize(), 5000);
    ref.current?.resize();
  }, []);

  if (isSSR) {
    return null;
  }

  return (
    <MapboxMap
      mapStyle="mapbox://styles/mapbox/light-v10"
      ref={ref}
      style={style}
      initialViewState={{
        longitude: (coordinate?.longitude || 0) + 0.0015,
        latitude: (coordinate?.latitude || 0) - (isMobile ? 0 : 0.002),
        zoom: 14,
      }}
      mapboxAccessToken={process.env.NEXT_PUBLIC_MAPBOX_MAP_API_KEY as string}
    >
      {coordinate && (
        <Marker
          longitude={coordinate?.longitude as number}
          latitude={coordinate?.latitude as number}
          anchor="bottom"
        >
          <IconButton
            component={Link}
            target="_blank"
            href={url || `http://maps.google.com/maps?q=${coordinate.latitude},${coordinate.longitude}`}
          >
            <LocationOn sx={{ fontSize: "4rem" }} />
          </IconButton>
        </Marker>
      )}
    </MapboxMap>
  );
};

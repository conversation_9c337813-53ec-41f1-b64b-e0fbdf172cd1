import { Box, SxProps, Theme, Typography } from "@mui/material";
import { Flex } from "../commonStyledComponents";
import Link from "../Link";

interface Props {
  title?: string;
  contact1?: string;
  contact2?: string;
  subTitle?: string;
  type: "instagram" | "facebook" | "phone" | "email" | "email-phone";
  sx?: SxProps<Theme> | undefined;
}

const prefixMap = {
  phone: "tel: ",
  email: "mailto: ",
  instagram: "https://www.instagram.com/",
  facebook: "https://www.facebook.com/",
};

export const ContactItem = ({
  title,
  contact1,
  contact2,
  subTitle,
  type,
  sx,
}: Props) => {
  const getPrefix = (level: 1 | 2) => {
    if (type === "email-phone") {
      return prefixMap[level === 1 ? "email" : "phone"];
    } else {
      return prefixMap[type];
    }
  };

  return (
    <Box sx={sx}>
      {title && (
        <Typography mb={1} fontSize="0.75rem" textTransform="uppercase">
          {title}
        </Typography>
      )}
      <Flex>
        <Link
          href={`${getPrefix(contact1?.includes("@") ? 1 : 2)}${contact1}`}
          sx={{
            fontFamily: "HrotBasic",
            color: "primary.main",
            fontSize: "1rem",
          }}
          target="_blank"
        >
          {(type === "instagram" || type === "facebook") && (
            <Box width={40} mr={1}>
              <img src={`/${type}.svg`} alt="Contact"/>
            </Box>
          )}
          {contact1}
        </Link>
      </Flex>
      {contact2 && (
        <Link
          href={`${getPrefix(contact2.includes("@") ? 1 : 2)}${contact2}`}
          sx={{
            fontFamily: "HrotBasic",
            color: "primary.main",
            my: 1,
            fontSize: "0.75rem",
          }}
        >
          {contact2}
        </Link>
      )}
      {subTitle && (
        <Typography mb={1} mt={contact2 ? 0 : 1}>
          {subTitle}
        </Typography>
      )}
    </Box>
  );
};

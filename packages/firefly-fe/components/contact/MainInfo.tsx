import { Box, Typography } from "@mui/material";
import { Flex } from "../commonStyledComponents";
import { ContactItem } from "./ContactItem";
import { Contact } from "./types";

interface Props {
  data?: Contact;
}

export const MainInfo = ({ data }: Props) => {
  return (
    <Flex
      flexDirection="column"
      justifyContent="center"
      ml={{ xs: 3, sm: 20 }}
      flexShrink={0}
    >
      <Typography variant="h1">Kontakt</Typography>
      <Box
        mt={10}
        maxHeight={{ _: "auto", sm: 400 }}
        sx={{
          columnCount: { xs: 1, sm: 2 },
        }}
        gap={"24px 64px"}
      >
        {data?.contactInfo
          .sort((c1, c2) => c1.order - c2.order)
          .map((c) => (
            <ContactItem
              key={c.title}
              title={c.title}
              subTitle={c.note}
              contact1={c.contact1}
              contact2={c.contact2}
              type="email-phone"
              sx={{ mb: 3, breakInside: "avoid-column" }}
            />
          ))}
        <ContactItem
          contact1={data?.instagram}
          type="instagram"
          sx={{ mb: 1 }}
        />
        <ContactItem contact1={data?.facebook} type="facebook" sx={{ mb: 1 }} />
      </Box>
    </Flex>
  );
};

export interface Contact {
    receptionPhone: string;
    email: string;
    studioManagerPhone: string;
    instagram: string;
    facebook: string;
    receptionOpenHours: string;
    studioManagerName: string;
    destinationDescription: string;
    contactInfo: {
        title: string;
        contact1: string;
        contact2: string;
        note: string;
        order: number;
    }[];
    locations: {
        type: string;
        name: string;
        address: string;
        url: string;
        coordinate: {
            longitude: number;
            latitude: number;
        }
    }[];
    englishContentInfo: string;
    englishTitleInfo: string;
}

export interface EnglishInfo {
    title: string;
    content: string;
}
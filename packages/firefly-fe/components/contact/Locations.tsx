import { Box, styled, Typography } from "@mui/material";
import { isSSR } from "../../helpers/image";
import AnimatingBlock from "../animatingBlock/AnimatingBlock";
import { Flex } from "../commonStyledComponents";
import { Map } from "./Map";
import { Contact } from "./types";

interface Props {
  data?: Contact;
}

export const Locations = ({ data }: Props) => {
  return (
    <Flex ml={{ xs: 0, sm: 8 }} flexDirection={{ xs: "column", sm: "row" }}>
      {data?.locations.map((location, index) => (
        <AnimatingBlock
          key={location.name}
          index={index}
          percentageHeight={70}
          disableAnimationOnMobile
          mobileHeight={100}
          sx={{ ml: { xs: 0, sm: 2 }, mt: { xs: 2, sm: 0 } }}
          width={350}
          render={({ maxHeight }) => (
            <ItemContainer
              height="100%"
              isodd={index % 2 === 0 ? "true" : undefined}
            >
              <Box p={3}>
                <Typography sx={{ fontSize: "1.2rem" }}>
                  {location.type}
                </Typography>
                <Typography
                  sx={{ fontFamily: "HrotBasic", fontSize: "1.5rem" }}
                >
                  {location.name}
                </Typography>
                <Typography>{location.address}</Typography>
              </Box>
              {!isSSR && (
                <Map
                  coordinate={location.coordinate}
                  url={location.url}
                  style={{
                    width: 490,
                    height: maxHeight === "100%" ? 200 : maxHeight,
                  }}
                />
              )}
            </ItemContainer>
          )}
        />
      ))}
    </Flex>
  );
};

const ItemContainer = styled(Box)<{ isodd?: string }>`
  position: relative;

  svg {
    color: ${({ theme, isodd }) =>
      isodd ? theme.palette.primary.main : theme.palette.secondary.main};
  }
`;

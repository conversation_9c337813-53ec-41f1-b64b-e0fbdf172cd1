import {
  <PERSON>,
  Button,
  IconButton,
  <PERSON>,
  <PERSON>,
  Typography,
} from "@mui/material";
import Markdown from "markdown-to-jsx";
import { useDialog } from "../../providers/DialogProvider";
import { Flex } from "../commonStyledComponents";
import { ContactItem } from "../contact/ContactItem";
import { Contact } from "../contact/types";

interface Props {
  data?: Contact;
}

export const MainInfo = ({ data }: Props) => {
  const { goTo } = useDialog();
  return (
    <Flex
      flexDirection="column"
      justifyContent="center"
      ml={{ xs: 3, sm: 20 }}
      mr={{ xs: 3, sm: 0 }}
      flexShrink={0}
    >
      <Box display="relative">
        <Typography sx={{ mb: 2 }}>{data?.destinationDescription}</Typography>
        <Typography variant="h1">{data?.englishTitleInfo}</Typography>
        <MarkdownContainer
          options={{
            overrides: {
              button: {
                component: <PERSON><PERSON>,
                props: {
                  onClick: () => goTo("registration"),
                  sx: {
                    padding: 0,
                    fontFamily: '"Roboto","Helvetica","Arial","sans-serif"',
                    fontSize: "0.875rem",
                    marginX: -0.5,
                    mt: -0.2,
                    letterSpacing: "0.00938em",
                    lineHeight: 1.5,
                  },
                },
              },
            },
          }}
          sx={{
            maxHeight: { sm: "30vh" },
            columnCount: {
              sx: 1,
            },
          }}
        >
          {data?.englishContentInfo || ""}
        </MarkdownContainer>
        <Box marginLeft={{ xs: 0, sm: "445px" }} mt={3}>
          <Typography fontSize="0.6rem">JARKA, STUDIO MANAGER</Typography>
          <ContactItem contact1={data?.email} type="email" sx={{ mt: 1 }} />
          <Flex alignItems="center">
            <ContactItem
              contact1={data?.studioManagerPhone}
              type="phone"
              sx={{ mb: 0, mr: 2 }}
            />
            <IconButton
              component={Link}
              target="_blank"
              href={`https://www.instagram.com/${data?.instagram}`}
            >
              <img src="/instagram.svg" width="20px" height="20px" alt="Instagram"/>
            </IconButton>
            <IconButton
              component={Link}
              target="_blank"
              href={`https://www.facebook.com/${data?.facebook}`}
            >
              <img src="/facebook.svg" width="20px" height="20px" alt="Facebook"/>
            </IconButton>
          </Flex>
        </Box>
      </Box>
    </Flex>
  );
};

const MarkdownContainer = styled(Markdown)`
  margin-top: 3rem;
  column-gap: 0;

  p {
    color: ${({ theme }) => theme.palette.text.primary};
    font-family: "Roboto", "Helvetica", "Arial", sans-serif;
    letter-spacing: 0.00938em;
    line-height: 1.5;
    font-size: 0.875rem;
    max-width: 400px;

    ${({ theme }) => ({
      [theme.breakpoints.up("sm")]: {
        marginRight: "3rem",
      },
      [theme.breakpoints.down("sm")]: {
        maxWidth: "100%",
      },
    })}
    margin-top: 0;

    a {
      color: ${({ theme }) => theme.palette.primary.main};
      font-weight: 700;
    }
  }

  @media (max-height: 719px) {
    column-count: 4;
  }
  @media (min-height: 720px) and (max-height: 1100px) {
    column-count: 3;
  }
  @media (min-height: 1101px) {
    column-count: 2;
  }

  ${({ theme }) => ({
    [theme.breakpoints.down("sm")]: {
      columnCount: 1,
    },
  })}
`;

import { useAnalytics } from "../providers/AnalyticsProvider";
import { Box, Button, styled } from "@mui/material";
import { Flex } from "./commonStyledComponents";

export const CookiesBanner = () => {
  const { cookies, allowCookies, disableCookies } = useAnalytics();

  const now = new Date().getTime();

  if (typeof window === "undefined") return null;
  if ((cookies?.hideUntil || 0) >= now) return null;

  return (
    <Container sx={{ zIndex: 10, flexDirection: { xs: "column", md: "row" } }}>
      <Box>
        Používáme cookies pro zlepšení našeho webu tak, aby se ti s ním
        pracovalo co nejlépe.
      </Box>
      <Flex ml={{ xs: 0, md: 6 }} mt={{ xs: 3, md: 0 }}>
        <Button variant="outlined" onClick={allowCookies}>
          Povolit
        </Button>
        <Box ml={3}>
          <Button onClick={disableCookies}><PERSON>ak<PERSON><PERSON>t</Button>
        </Box>
      </Flex>
    </Container>
  );
};

const Container = styled(Flex)`
  position: fixed;
  left: 32px;
  padding: 0.75rem;
  border-radius: 0.5rem;
  bottom: 32px;
  background: ${({ theme }) => theme.palette.secondary.main};
  color: white;
  justify-content: center;
  align-items: center;
  color: black;

  ${(props) => props.theme.breakpoints.down("md")} {
    right: 32px;
  }
`;

import { DoubleArrow } from "@mui/icons-material";
import {
  Box,
  Button,
  IconButton,
  styled,
  SwipeableDrawer,
  Typography
} from "@mui/material";
import Logo from "../../assets/firefly-icon-logo.svg";
import { useIsMobile } from "../../helpers/device";
import { useDialog } from "../../providers/DialogProvider";
import { Flex } from "../commonStyledComponents";
import Link from "../Link";

interface Props {
  children?: React.ReactNode;
  title?: string;
  hideBack?: boolean;
}

export const RightSwipingDialog = ({ children, title, hideBack }: Props) => {
  const isMobile = useIsMobile();
  const { goBack, dialog, close } = useDialog();
  return (
    <SwipeableDrawer
      anchor="right"
      open={dialog.isOpen}
      onOpen={() => close()}
      onClose={() => close()}
      PaperProps={{
        sx: {
          padding: '0 !important'
        }
      }}
    >
      <Flex
        width={isMobile ? window.innerWidth : 450}
        flexDirection="column"
        overflow="hidden"
      >
        <Flex sx={{ justifyContent: "space-between", p: 1, ml: 1 }}>
          <IconButton onClick={() => close()}>
            <DoubleArrow sx={{ color: "secondary.main", fontSize: "3rem" }} />
          </IconButton>
          <Link href="/">
            <Logo
              alt="Firefly icon logo"
              color="white"
              width={55}
              height={54}
            />
          </Link>
        </Flex>
        {dialog.slides.length > 1 && !hideBack && (
          <BackLink
            color="secondary"
            variant="text"
            startIcon={<DoubleArrow />}
            onClick={goBack}
          >
            Zpět
          </BackLink>
        )}
        {title && (
          <Typography variant="h5" color="common.white" px={4} pt={3}>
            {title}
          </Typography>
        )}
        <ScrollContainer mt={title ? 3 : 0}>{children}</ScrollContainer>
      </Flex>
    </SwipeableDrawer>
  );
};

const BackLink = styled(Button)`
  font-size: 1.1rem;
  padding-left: 0;
  padding-right: 0;
  font-family: "HrotBasic";
  align-self: flex-start;
  margin-left: 32px;

  svg {
    transform: rotate(-180deg);
  }
`;

const ScrollContainer = styled(Box)`
  overflow: auto;
  padding: 0 32px 32px 32px;
`;

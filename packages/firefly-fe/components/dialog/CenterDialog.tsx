import { DoubleArrow } from "@mui/icons-material";
import { Box, Button, Dialog, styled, Typography } from "@mui/material";
import { useDialog } from "../../providers/DialogProvider";
import { Flex } from "../commonStyledComponents";

interface Props {
  children?: React.ReactNode;
  title?: string;
}

export const CenterDialog = ({ children, title }: Props) => {
  const { goBack, dialog, close } = useDialog();
  return (
    <Dialog onClose={() => close()} open={dialog.isOpen}>
      <Flex flexDirection="column" overflow="hidden">
        <Flex sx={{ justifyContent: "flex-end", p: 1, ml: 1 }}>
          <Button
            onClick={() => close()}
            variant="outlined"
            color="primary"
            size="small"
          >
            <PERSON><PERSON><PERSON><PERSON>t
          </Button>
        </Flex>
        {dialog.slides.length > 1 && (
          <BackLink
            color="secondary"
            variant="text"
            startIcon={<DoubleArrow />}
            onClick={goBack}
          >
            <PERSON><PERSON><PERSON><PERSON>
          </BackLink>
        )}
        {title && (
          <Typography variant="h5" color="common.white" px={4} pt={3}>
            {title}
          </Typography>
        )}
        <ScrollContainer px={{ xs: 0, sm: 3 }}>{children}</ScrollContainer>
      </Flex>
    </Dialog>
  );
};

const BackLink = styled(Button)`
  font-size: 1.1rem;
  padding-left: 0;
  padding-right: 0;
  font-family: "HrotBasic";
  align-self: flex-start;
  margin-left: 32px;

  svg {
    transform: rotate(-180deg);
  }
`;

const ScrollContainer = styled(Box)`
  overflow: auto;
  padding-bottom: 32px;
  margin-top: 16px;
`;

import { DoubleArrow } from "@mui/icons-material";
import { Button, IconButton, styled, SxProps, Theme } from "@mui/material";

interface Props {
  showMore: boolean;
  setShowMore: (show: boolean) => void;
  sx?: SxProps<Theme> | undefined;
  children?: React.ReactNode;
}

export const ShowMoreButton = ({
  showMore,
  setShowMore,
  sx,
  children,
}: Props) => {
  if (children == null) {
    return (
      <OpenIconButton
        onClick={() => setShowMore(!showMore)}
        sx={sx}
        aria-label="show more"
      >
        <DoubleArrow
          sx={{ transform: `rotate(${showMore ? "-90deg" : "90deg"})` }}
        />
      </OpenIconButton>
    );
  }
  return (
    <OpenButton
      onClick={() => setShowMore(!showMore)}
      aria-label="show more"
      sx={sx}
      endIcon={<DoubleArrow />}
      showmore={showMore ? "true" : undefined}
    >
      {children}
    </OpenButton>
  );
};

const OpenButton = styled(Button)<{ showmore?: string }>`
  svg {
    color: ${({ theme }) => theme.palette.primary.main};
    transform: ${({ showmore }) => `rotate(${showmore ? "-90deg" : "90deg"})`};
  }
`;

const OpenIconButton = styled(IconButton)`
  svg {
    color: ${({ theme }) => theme.palette.primary.main};
  }
`;

import { axios<PERSON>pi, ReservantoAppVersion } from "../../axios";

export const getClientLongTimeToken = async (): Promise<any> => {
  const response = await axiosApi(
    `/reservanto/Authorize/GetClientLongTimeToken`,
    {
      params: {
        appVersion: ReservantoAppVersion.v1,
        clientId: process.env.NEXT_PUBLIC_RESERVANTO_CLIENT_ID,
        rights: ["BookingResource_cr", "BookingService_cr", "Location_cr"],
      },
    }
  );
  return response.data;
};

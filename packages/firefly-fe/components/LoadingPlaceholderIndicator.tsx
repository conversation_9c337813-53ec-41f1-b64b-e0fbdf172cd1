import styled from "@emotion/styled";
import { Box, BoxProps, Typography, keyframes } from "@mui/material";
import { useEffect, useState } from "react";

interface Props {
  sx?: BoxProps["sx"];
  transparent?: boolean;
}

export const LoadingPlaceholderIndicator = ({ sx, transparent }: Props) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 300);

    return () => {
      clearTimeout(timer);
    };
  }, []);

  if (!isVisible) {
    return null;
  }
  if (transparent) {
    return (
      <LoadingWrapperTrans sx={sx}>
        <ActivityTrans />
      </LoadingWrapperTrans>
    );
  }
  return (
    <LoadingWrapper sx={sx}>
      <Activity />
    </LoadingWrapper>
  );
};

const LoadingWrapper = styled(Typography)`
  position: relative;
  background-color: rgb(240 234 229);
  z-index: 44;
  overflow: hidden;
`;

const LoadingWrapperTrans = styled(Box)`
  position: relative;
  background-color: rgb(0 0 0 / 8%);
  z-index: 44;
  overflow: hidden;
`;

const loading = keyframes`
    0%{
      left: -45%;
    }
    100%{
      left: 100%;
    }
  `;

const ActivityTrans = styled(Typography)`
  position: absolute;
  left: -45%;
  height: 100%;
  width: 45%;
  background-image: linear-gradient(
    to left,
    rgb(0 0 0 / 2%),
    rgb(0 0 0 / 5%),
    rgb(0 0 0 / 10%),
    rgb(0 0 0 / 5%),
    rgb(0 0 0 / 2%)
  );
  background-image: -moz-linear-gradient(
    to left,
    rgb(0 0 0 / 2%),
    rgb(0 0 0 / 5%),
    rgb(0 0 0 / 10%),
    rgb(0 0 0 / 5%),
    rgb(0 0 0 / 2%)
  );
  background-image: -webkit-linear-gradient(
    to left,
    rgb(0 0 0 / 2%),
    rgb(0 0 0 / 5%),
    rgb(0 0 0 / 10%),
    rgb(0 0 0 / 5%),
    rgb(0 0 0 / 2%)
  );
  animation: ${loading} 1s infinite;
  z-index: 45;
`;

const Activity = styled(Typography)`
  position: absolute;
  left: -45%;
  height: 100%;
  width: 45%;
  background-image: linear-gradient(
    to left,
    rgba(251, 251, 251, 0.05),
    rgba(251, 251, 251, 0.3),
    rgba(251, 251, 251, 0.6),
    rgba(251, 251, 251, 0.3),
    rgba(251, 251, 251, 0.05)
  );
  background-image: -moz-linear-gradient(
    to left,
    rgba(251, 251, 251, 0.05),
    rgba(251, 251, 251, 0.3),
    rgba(251, 251, 251, 0.6),
    rgba(251, 251, 251, 0.3),
    rgba(251, 251, 251, 0.05)
  );
  background-image: -webkit-linear-gradient(
    to left,
    rgba(251, 251, 251, 0.05),
    rgba(251, 251, 251, 0.3),
    rgba(251, 251, 251, 0.6),
    rgba(251, 251, 251, 0.3),
    rgba(251, 251, 251, 0.05)
  );
  animation: ${loading} 1s infinite;
  z-index: 45;
`;

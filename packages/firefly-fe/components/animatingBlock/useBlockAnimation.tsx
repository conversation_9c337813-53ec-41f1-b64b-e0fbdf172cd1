import { useEffect, useState } from "react";
import { useSpring } from "react-spring";
import { isSSR } from "../../helpers/image";
import { useIsMobile } from "../../helpers/device";
import { isMobile as isMobileDetect, isTablet } from "react-device-detect";

interface Props {
  index: number;
  percentageHeight: number;
  width: number;
  animatedContentHeight: number;
  disableAnimationOnMobile?: boolean;
  disableHeightAnimation?: boolean;
  mobileHeight?: number;
}

const defaultSSRWindowHeight = 500;

const useBlockAnimation = ({
  index,
  width,
  percentageHeight,
  animatedContentHeight,
  disableAnimationOnMobile,
  disableHeightAnimation,
  mobileHeight,
}: Props) => {
  const [isHovering, setIsHovering] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const isMobileQuery = useIsMobile();
  const isMobile = (isMobileDetect && !isTablet) || isMobileQuery;

  useEffect(() => {
    if (isHovering && !isOpen && isMobile) {
      setIsOpen(true);
    }
  }, [isHovering, isOpen, isMobile]);

  const isOdd = index % 2 === 0;
  const windowHeight = !isSSR ? window.innerHeight : defaultSSRWindowHeight;
  const correctWidth = isMobile ? window.innerWidth : width;
  const isZoomed =
    isHovering || (isMobile && disableAnimationOnMobile) || isOpen;

  const boxDefaultHeight = isMobile
    ? mobileHeight || window.innerWidth
    : (percentageHeight / 100) * windowHeight + (isOdd ? 0 : 40);

  const maxHeight = isMobile
    ? boxDefaultHeight + animatedContentHeight
    : disableHeightAnimation
    ? boxDefaultHeight
    : boxDefaultHeight + (isOdd ? boxDefaultHeight / 4 : boxDefaultHeight / 5);

  const zoomInAnimation = useSpring({
    height: isZoomed
      ? isMobile && disableAnimationOnMobile
        ? "100%"
        : maxHeight
      : boxDefaultHeight,
    minHeight: mobileHeight,
    width: isHovering ? correctWidth * (isMobile ? 1 : 1.4) : correctWidth,
  });

  const changeContentHeightAnimation = useSpring({
    height: isZoomed ? animatedContentHeight : 0,
  });

  const opacityContentAnimation = useSpring({
    opacity: isZoomed ? 1 : 0,
  });

  return { zoomInAnimation, changeContentHeightAnimation, opacityContentAnimation, onMouseEnter: () => setIsHovering(true), onMouseLeave: () => setIsHovering(false), maxHeight };
};

export default useBlockAnimation;

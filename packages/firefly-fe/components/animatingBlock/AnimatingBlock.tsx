import { Box, styled, SxProps, Theme } from "@mui/material";
import { animated, SpringValue } from "react-spring";
import useBlockAnimation from "./useBlockAnimation";

interface Props {
  percentageHeight: number;
  animatedContentHeight?: number;
  disableAnimationOnMobile?: boolean;
  disableHeightAnimation?: boolean;
  mobileHeight?: number;
  noBackground?: boolean;
  width: number;
  index: number;
  render: ({
    zoomedContentStyle,
    opacityContentStyle,
    maxHeight,
  }: {
    zoomedContentStyle: {
      height: SpringValue<number>;
      overflow: string;
    };
    opacityContentStyle: {
      opacity: SpringValue<number>;
    };
    maxHeight: string | number;
  }) => React.ReactNode;
  sx?: SxProps<Theme>;
}

const AnimatingBox = ({
  index,
  render,
  animatedContentHeight = 200,
  disableAnimationOnMobile,
  disableHeightAnimation,
  noBackground,
  sx,
  ...rest
}: Props) => {
  const {
    onMouseEnter,
    onMouseLeave,
    zoomInAnimation,
    changeContentHeightAnimation,
    opacityContentAnimation,
    maxHeight,
  } = useBlockAnimation({
    ...rest,
    index,
    animatedContentHeight,
    disableAnimationOnMobile,
    disableHeightAnimation,
  });

  return (
    <AnimatingBoxContainer
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
      isodd={index % 2 === 0 ? "true" : undefined}
      nobackground={noBackground ? "true" : undefined}
      sx={sx}
    >
      <animated.div style={{ ...zoomInAnimation, display: "flex" }}>
        {render({
          zoomedContentStyle: {
            ...changeContentHeightAnimation,
            overflow: "hidden",
          },
          opacityContentStyle: opacityContentAnimation,
          maxHeight,
        })}
      </animated.div>
    </AnimatingBoxContainer>
  );
};

export default AnimatingBox;

const AnimatingBoxContainer = styled(Box)<{
  isodd?: string;
  nobackground?: string;
}>`
  overflow: hidden;
  align-self: flex-end;
  background-color: ${({ theme, isodd, nobackground }) =>
    nobackground
      ? "transparent"
      : isodd
      ? theme.palette.primary.main
      : theme.palette.secondary.main};

  p,
  h4,
  h5,
  h6 {
    color: ${({ theme, isodd, nobackground }) =>
      !isodd || nobackground
        ? theme.palette.text.primary
        : theme.palette.text.secondary};
  }

  a {
    color: ${({ theme, isodd }) =>
      isodd ? theme.palette.secondary.main : theme.palette.primary.main};
    font-weight: 900;
    font-size: 1.1rem;
  }

  > div > div {
    flex-grow: 1;
  }
`;

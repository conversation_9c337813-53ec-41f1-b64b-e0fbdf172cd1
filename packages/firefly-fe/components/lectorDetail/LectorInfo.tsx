import { Box, Typography } from "@mui/material";
import { <PERSON><PERSON> } from "../../strapi/types";
import { Flex } from "../commonStyledComponents";
import { Contact } from "./Contact";
import Markdown from "markdown-to-jsx";

interface Props {
  data?: Lector;
}

export const LectorInfo = ({ data }: Props) => {
  return (
    <Flex position="relative" justifyContent="center" flexShrink={0}>
      <Flex
        flexDirection="column"
        flexWrap="wrap"
        alignSelf="center"
        maxHeight="75%"
        mb={{ xs: 19, sm: 0 }}
        width={{ xs: "100%", sm: "auto" }}
        flexShrink={0}
      >
        {data?.content?.map((ct) => (
          <Box
            key={ct.title}
            maxWidth={{ xs: "auto", sm: 400 }}
            py={3}
            px={6}
            width={{ xs: "100%", sm: "auto" }}
          >
            <Typography variant="h5">{ct.title}</Typography>
            <Typography py={3}>
              <Markdown>{ct.text.replaceAll("\n", "<br />")}</Markdown>
            </Typography>
          </Box>
        ))}
      </Flex>
      <Contact data={data} />
    </Flex>
  );
};

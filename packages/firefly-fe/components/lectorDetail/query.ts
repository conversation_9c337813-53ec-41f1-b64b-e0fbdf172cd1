import { QueryFunctionContext } from "@tanstack/react-query";
import { axiosCms, StrapiArrayResponse } from "../../axios";
import { Lector } from "../../strapi/types";

export const getLector = async ({
  queryKey,
}: QueryFunctionContext<[string, { slug: string }]>): Promise<
  StrapiArrayResponse<Lector>
> => {
  const [, { slug }] = queryKey;
  const response = await axiosCms.get(`/lectors`, {
    params: {
      "filters[slug]": slug,
      populate: "deep,3",
    },
  });
  return response.data;
};

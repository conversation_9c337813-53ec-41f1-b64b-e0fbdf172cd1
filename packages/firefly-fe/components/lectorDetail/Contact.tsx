import { Box, Button, styled } from "@mui/material";
import { Le<PERSON> } from "../../strapi/types";
import Link from "../Link";

interface Props {
  data?: Lector;
}

export const Contact = ({ data }: Props) => {
  return (
    <ContactContainer ml={6} mb={6} mt={6}>
      {!!data?.instagram && (
        <Link
          target="_blank"
          sx={{ fontFamily: "HrotBasic", mt: 2 }}
          href={`https://www.instagram.com/${data.instagram}`}
        >
          <IgIcon
            src="/instagram.svg"
            alt="Instagram"
            width="22px"
            height="22px"
            sx={{ mr: 1 }}
          />
          {data.instagram}
        </Link>
      )}
      {!!data?.phoneNumber && (
        <Link
          href={`tel: ${data?.phoneNumber}`}
          sx={{ fontFamily: "HrotBasic", mt: 2 }}
        >
          {data?.phoneNumber}
        </Link>
      )}
      <Link
        href={`mailto: ${data?.email}`}
        sx={{ fontFamily: "HrotBasic", mt: 2 }}
      >
        {data?.email}
      </Link>
    </ContactContainer>
  );
};

const ContactContainer = styled(Box)`
  position: absolute;
  left: 0;
  bottom: 0;
`;

const IgIcon = styled("img")``;

import DoubleArrow from "@mui/icons-material/DoubleArrow";
import { Box, styled, Typography } from "@mui/material";
import Image from "next/image";
import { getImageUrl } from "../../helpers/image";
import { routes } from "../../routes";
import { Lector } from "../../strapi/types";
import { Flex } from "../commonStyledComponents";
import Link from "../Link";

interface Props {
  data?: Lector;
  prevUrl?: string;
}

export const PhotoWithName = ({ data, prevUrl }: Props) => {
  const isFromSameDomain = prevUrl?.includes(
    process.env.NEXT_PUBLIC_APP_URL || ""
  );
  const [, prevPathname] = isFromSameDomain
    ? prevUrl?.split(process.env.NEXT_PUBLIC_APP_URL || "") || []
    : [];

  return (
    <Flex
      alignItems="flex-start"
      flexDirection="column"
      id="lectorImage"
      justifyContent="center"
      position={{ xs: "relative", sm: "relative" }}
      width={{ xs: "auto", sm: "100vh" }}
      minWidth={{ xs: 300, sm: 800 }}
      mx={{ xs: 5, sm: 0 }}
      my={{ xs: 5, sm: 0 }}
      px={{ xs: 0, sm: 10 }}
    >
      <LectorName
        variant="h1"
        color="background.default"
        fontSize={{ xs: "4rem", sm: "6rem" }}
        textAlign="start"
        py={{ xs: 3, sm: 0 }}
      >
        {data?.name.split(" ").map((name) => (
          <>
            {name} <br />
          </>
        ))}
      </LectorName>
      {data?.image.data.attributes.url && (
        <BackgroundImage>
          <Image
            src={getImageUrl(data.image.data.attributes.url)}
            alt={data.name}
            objectFit="cover"
            layout="fill"
          />
          <BackgroundOverlay />
        </BackgroundImage>
      )}
      {prevPathname && prevPathname.includes(routes.activities) && (
        <BackLink
          color="secondary"
          startIcon={<DoubleArrow />}
          href={`${prevPathname}#lectors`}
        >
          Zpět na lektory
        </BackLink>
      )}
      {prevPathname && prevPathname.includes(routes.booking) && (
        <BackLink
          color="secondary"
          startIcon={<DoubleArrow />}
          href={prevPathname}
        >
          Zpět na rozvrh
        </BackLink>
      )}
    </Flex>
  );
};

const BackLink = styled(Link)`
  margin-top: 36px;
  font-size: 1rem;
  font-weight: 700;
  font-family: "HrotBasic";

  svg {
    transform: rotate(-180deg);
  }
`;

const BackgroundOverlay = styled(Box)`
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background-color: ${({ theme }) => theme.palette.primary.main};
  mix-blend-mode: multiply;
`;

const BackgroundImage = styled(Box)`
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  z-index: -1;

  ${(props) => props.theme.breakpoints.down("sm")} {
    top: -40px;
    min-height: 100%;
    width: auto;
    left: -40px;
    right: -40px;
    bottom: -40px;
  }
`;

const LectorName = styled(Typography)`
  ${(props) => props.theme.breakpoints.down("md")} {
    font-size: min(2rem, 7vh);
  }
`;

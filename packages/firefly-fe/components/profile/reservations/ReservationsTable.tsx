import {
  <PERSON>lapse,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
} from "@mui/material";
import { AlternateEvent, Event } from "../../../reservanto/types";
import { ReservationItemLesson } from "./ReservationItemLesson";
import { ReservationItemCourse } from "./ReservationItemCourse";
import { AlternateItemCourse } from "./AlternateItemCourse";
import { AlternateItemLesson } from "./AlternateItemLesson";
import { isAlternate, isCourse } from "./helpers";
import { getTimeFromUnixTimeStamp } from "../../booking/helper";

interface Props {
  data: { [key: string]: (Event | AlternateEvent)[] };
  showAll: boolean;
}

export const SHOW_AT_LEAST = 3;

export const ReservationsTable = ({ data, showAll }: Props) => {
  const now = new Date();

  const sortedDataKeys = Object.keys(data).sort((a, b) => {
    // Find the closest upcoming lesson date for key 'a'
    const upcomingA = data[a].map(event => getTimeFromUnixTimeStamp(event.StartDate || 0))
      .filter(date => date > now)
      .sort((date1, date2) => date1.getTime() - date2.getTime())[0]; // Sort and get the earliest future date

    // Find the closest upcoming lesson date for key 'b'
    const upcomingB = data[b].map(event => getTimeFromUnixTimeStamp(event.StartDate || 0))
      .filter(date => date > now)
      .sort((date1, date2) => date1.getTime() - date2.getTime())[0]; // Sort and get the earliest future date

    // Handle cases where there are no future dates
    if (!upcomingA) return 1; // Place 'a' after 'b' if 'a' has no future dates
    if (!upcomingB) return -1; // Place 'b' after 'a' if 'b' has no future dates

    // Compare the two upcoming dates
    return upcomingA.getTime() - upcomingB.getTime();
  });


  const renderItem = (data: (Event | AlternateEvent)[], key: string) => {
    if (isCourse(data)) {
      if (isAlternate(data)) {
        return (
          <AlternateItemCourse
            data={data as AlternateEvent[]}
            key={key}
            courseId={data?.[0].CourseId!}
          />
        );
      }
      return (
        <ReservationItemCourse
          data={data as Event[]}
          key={key}
          courseId={data?.[0].CourseId!}
        />
      );
    }

    // is individual appointment
    if (isAlternate(data)) {
      return <AlternateItemLesson data={data as AlternateEvent[]} key={key} />;
    }
    return <ReservationItemLesson data={data as Event[]} key={key} />;
  };

  return (
    <>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell width="22%">Příští lekce</TableCell>
            <TableCell width="20%">Lekce / kurz</TableCell>
            <TableCell width="12.5%">Cena</TableCell>
            <TableCell width="12.5%">Uhrazeno</TableCell>
            <TableCell width="30%" />
            <TableCell width="5%" />
          </TableRow>
        </TableHead>
        <TableBody>
          {sortedDataKeys
            ?.slice(0, SHOW_AT_LEAST)
            ?.map((key) => renderItem(data[key], key))}
        </TableBody>
      </Table>
      <Collapse in={showAll} timeout="auto" unmountOnExit>
        <Table>
          <TableBody>
            {sortedDataKeys
              ?.slice(SHOW_AT_LEAST, sortedDataKeys.length || 0)
              .map((key) => renderItem(data[key], key))}
          </TableBody>
        </Table>
      </Collapse>
    </>
  );
};

import { Box, CircularProgress, Paper, Typography } from "@mui/material";
import { groupBy } from "rambda";
import { useState } from "react";
import { AlternateEvent, Event } from "../../../reservanto/types";
import { Flex } from "../../commonStyledComponents";
import { ShowMoreButton } from "../../ShowMoreButton";
import { MobileReservations } from "./mobile/MobileReservations";
import { ReservationsTable, SHOW_AT_LEAST } from "./ReservationsTable";

interface Props {
  reservations: (Event | AlternateEvent)[] | undefined;
  isLoading: boolean;
}

export const Reservations = ({ reservations, isLoading }: Props) => {
  const [showMore, setShowMore] = useState(false);

  const groupedReservations = groupBy((item) => {
    const uniqKey = `${item.CalendarId}-${item.BookingServiceId}-${item.ServiceName}-${item.SourceName}-${item.CourseId}`;
    if (item.CourseId) {
      const lessons = reservations?.filter((r) => r.CourseId === item.CourseId);
      if ((lessons?.length || 0) > 1) {
        return uniqKey;
      }
    }
    return `${uniqKey}_${item.AppointmentId}`;
  }, reservations || []);

  const groupedReservationsKeys = Object.keys(groupedReservations);

  return (
    <Box mt={5} width="100%">
      <Typography variant="h4">Rezervace</Typography>
      <Box sx={{ display: { md: "none" }, mt: 2 }}>
        {!!reservations?.length ? (
          <>
            <MobileReservations data={groupedReservations} showAll={showMore} />
            <Flex justifyContent="flex-end">
              {(groupedReservationsKeys?.length || 0) > SHOW_AT_LEAST && (
                <ShowMoreButton
                  showMore={showMore}
                  setShowMore={setShowMore}
                  sx={{ alignSelf: "flex-end" }}
                >
                  {showMore ? "Zobrazit méně" : "Všechny rezervace"}
                </ShowMoreButton>
              )}
            </Flex>
          </>
        ) : (
          <Typography>Nemáš žádné rezervace.</Typography>
        )}
      </Box>
      <Paper
        sx={{
          display: { xs: "none", md: "flex" },
          mt: 2,
          flexDirection: "column",
        }}
      >
        {isLoading && !reservations ? (
          <CircularProgress sx={{ alignSelf: "center" }} />
        ) : !!reservations?.length ? (
          <>
            <ReservationsTable data={groupedReservations} showAll={showMore} />
            <Flex justifyContent="flex-end">
              {(groupedReservationsKeys?.length || 0) > SHOW_AT_LEAST && (
                <ShowMoreButton
                  showMore={showMore}
                  setShowMore={setShowMore}
                  sx={{ alignSelf: "flex-end", mt: 3 }}
                >
                  {showMore ? "Zobrazit méně" : "Všechny rezervace"}
                </ShowMoreButton>
              )}
            </Flex>
          </>
        ) : (
          <Typography>Nemáš žádné rezervace.</Typography>
        )}
      </Paper>
    </Box>
  );
};

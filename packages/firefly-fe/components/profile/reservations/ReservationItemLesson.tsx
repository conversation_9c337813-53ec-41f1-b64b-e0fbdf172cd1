import { Button } from "@mui/material";
import { useDialog } from "../../../providers/DialogProvider";
import { Event } from "../../../reservanto/types";
import { bankInfo, getAmountToPay } from "../../booking/payment/helpers";
import { useBooking } from "../../booking/useBooking";
import { CancelBookingButton } from "./CancelBookingButton";
import { getTotalPaidForLesson, getTotalPriceForLesson } from "./helpers";
import { ItemLayout } from "./ItemLayout";
import { MobileItemLayout } from "./mobile/MobileItemLayout";

interface Props {
  data?: Event[];
  isMobile?: boolean;
}

export const ReservationItemLesson = ({
  data: lessons = [],
  isMobile,
}: Props) => {
  const { cancelBooking } = useBooking();
  const { goTo } = useDialog();
  const lesson = lessons[0];

  const handleCancel = (appointmentId: number, cancelAllCourse?: boolean) => {
    cancelBooking.mutate({
      appointmentId,
      enableCancelWholeCourse: cancelAllCourse,
    });
  };

  const handlePayment = () => {
    const amount = getAmountToPay([lesson]);
    goTo(
      "payment",
      {
        appointmentId: lesson?.AppointmentId,
        courseId: lesson?.CourseId,
        bookingServiceId: lesson?.BookingServiceId,
        payment: {
          amount,
          type: lesson?.CalendarName as keyof typeof bankInfo,
        },
      },
      "center"
    );
  };

  const renderActionButtons = () => {
    const now = new Date().getTime() / 1000;
    const isOld = lesson.StartDate < now;
    const payedByReplacement = lesson?.PaymentMethodFormatted === "Náhrady";
    const payedBySeasonPass = lesson?.PaymentMethodFormatted === "Permanentky";

    if(lesson == null){
      return null;
    }

    return (
      <>
        {!lesson.IsPaid && !isOld && (
          <Button
            variant="outlined"
            color="primary"
            size="medium"
            sx={{
              whiteSpace: "nowrap",
            }}
            onClick={handlePayment}
          >
            Zaplatit
          </Button>
        )}
        <CancelBookingButton
          appointmentId={lesson?.AppointmentId}
          sx={{ float: "right" }}
          onCancel={handleCancel}
          appointmentStart={lesson?.StartDate}
          isPaid={lesson.IsPaid || !!payedByReplacement || !!payedBySeasonPass}
          totalPaid={lesson.PaidPartOfPrice}
          size="medium"
          isSpecificLesson
        />
      </>
    );
  };

  if (isMobile) {
    return (
      <MobileItemLayout
        data={lessons}
        totalPaid={getTotalPaidForLesson(lesson)}
        totalPrice={getTotalPriceForLesson(lesson)}
        renderActionButtons={renderActionButtons}
      />
    );
  }

  return (
    <ItemLayout
      data={lessons}
      totalPaid={getTotalPaidForLesson(lesson)}
      totalPrice={getTotalPriceForLesson(lesson)}
      renderActionButtons={renderActionButtons}
    />
  );
};

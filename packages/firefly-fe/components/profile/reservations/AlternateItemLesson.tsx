import { AlternateEvent } from "../../../reservanto/types";
import { useBooking } from "../../booking/useBooking";
import { Flex } from "../../commonStyledComponents";
import { CancelBookingButton } from "./CancelBookingButton";
import { ItemLayout } from "./ItemLayout";
import { MobileItemLayout } from "./mobile/MobileItemLayout";
import AccessTimeIcon from "@mui/icons-material/AccessTime";

interface Props {
  data?: AlternateEvent[];
  isMobile?: boolean;
}

export const AlternateItemLesson = ({
  data: lessons = [],
  isMobile,
}: Props) => {
  const { cancelBooking } = useBooking();

  const lesson = lessons[0];

  const handleCancel = (appointmentId: number, cancelAllCourse?: boolean) => {
    cancelBooking.mutate({
      appointmentId,
      isAlternate: true,
      courseId: cancelAllCourse ? lesson.CourseId : undefined,
    });
  };

  const renderActionButtons = () => {
    return (
      <CancelBookingButton
        isAlternate
        onCancel={handleCancel}
        appointmentStart={lesson.StartDate}
        appointmentId={lesson.AppointmentId}
      />
    );
  };

  const renderTotalPaid = () => {
    return (
      <Flex alignItems="center">
        <AccessTimeIcon sx={{ mr: 1 }} />
        <b>Náhradník</b>
      </Flex>
    );
  };

  if (isMobile) {
    return (
      <MobileItemLayout
        isPaid={false}
        data={lessons}
        totalPrice={`${lessons?.[0]?.Price} Kč`}
        totalPaid={renderTotalPaid()}
        renderActionButtons={renderActionButtons}
      />
    );
  }

  return (
    <ItemLayout
      isPaid={false}
      data={lessons}
      totalPrice={`${lessons?.[0]?.Price} Kč`}
      totalPaid={renderTotalPaid()}
      renderActionButtons={renderActionButtons}
    />
  );
};

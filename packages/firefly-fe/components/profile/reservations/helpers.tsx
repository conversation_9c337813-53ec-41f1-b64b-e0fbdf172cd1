import { CircularProgress } from "@mui/material";
import {
  AlternateEvent,
  CourseCustomerEvents,
  Event,
} from "../../../reservanto/types";

export const isCourse = (events: (Event | AlternateEvent)[]) =>
  events?.length > 1 && !!events[0].CourseId;
export const isAlternate = (events: (Event | AlternateEvent)[]) =>
  events?.length > 0 && events[0].Status === "waiting";

export const getTotalPaidForLesson = (lesson: Event) => {
  const payedByReplacement = lesson?.PaymentMethodFormatted === "Náhrady";
  const payedBySeasonPass = lesson?.PaymentMethodFormatted === "Permanentky";
  if (payedByReplacement) {
    return "Náhradou";
  }
  if (payedBySeasonPass) {
    return "Permanentkou";
  }
  if (lesson.PaidPartOfPrice === 0) {
    return "0 Kč";
  }
  if (lesson.IsPaid) {
    return "Ano";
  }
  return `${lesson.PaidPartOfPrice} Kč`;
};

export const getTotalPaidForCourse = (course?: CourseCustomerEvents | null) => {
  if (course == null) {
    return <CircularProgress size="1.2rem" />;
  }
  if (course.PaidPartOfPriceWithVat === 0) {
    return "0 Kč";
  }
  if (
    course.PaidPartOfPriceWithVat !== course.TotalCoursePriceWithVat &&
    course.PaidPartOfPriceWithVat === 500
  ) {
    return `Záloha ${course.PaidPartOfPriceWithVat} Kč`;
  }
  if (course.IsPaid) {
    return "Ano";
  }
  return `${course.PaidPartOfPriceWithVat} Kč`;
};

export const getTotalPriceForLesson = (lesson: Event) =>
  lesson?.Price === 0 ? "-" : `${lesson.Price} Kč`;

export const getTotalPriceForCourse = (
  course?: CourseCustomerEvents | null
) => {
  if (course == null) {
    return <CircularProgress size="1.2rem" />;
  }

  return course.TotalCoursePriceWithVat === 0 || course.IsPaid
    ? "-"
    : `${course.TotalCoursePriceWithVat} Kč`;
};

export const getPayButtonText = (
  lessons: Event[],
  data?: CourseCustomerEvents
) => {
  if (lessons.length > 2) {
    if (data?.PaidPartOfPriceWithVat === 0) {
      return "Zaplatit zálohu";
    }
    return "Doplatit kurz";
  }
  return "Zaplatit";
};

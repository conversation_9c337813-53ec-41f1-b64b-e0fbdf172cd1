import { AlternateEvent } from "../../../reservanto/types";
import { useBooking } from "../../booking/useBooking";
import { Flex } from "../../commonStyledComponents";
import { CancelBookingButton } from "./CancelBookingButton";
import { ItemLayout } from "./ItemLayout";
import { MobileItemLayout } from "./mobile/MobileItemLayout";
import AccessTimeIcon from "@mui/icons-material/AccessTime";

interface Props {
  data?: AlternateEvent[];
  courseId: number;
  isMobile?: boolean;
}

export const AlternateItemCourse = ({
  data: lessons = [],
  isMobile = false,
}: Props) => {
  const { cancelBooking } = useBooking();

  const handleCancel = (appointmentId: number) => {
    cancelBooking.mutate({
      appointmentId,
      isAlternate: true,
      courseId: lessons[0].CourseId,
    });
  };

  const renderTotalPaid = () => {
    return (
      <Flex alignItems="center">
        <AccessTimeIcon sx={{ mr: 1 }} />
        <b>Náhradník</b>
      </Flex>
    );
  };

  const renderActionButtons = () => {
    return (
      <CancelBookingButton
        isAlternate
        onCancel={handleCancel}
        appointmentStart={lessons[0].StartDate}
        appointmentId={lessons[0].AppointmentId}
      />
    );
  };

  const totalPrice = lessons?.reduce((prev, lesson) => lesson.Price + prev, 0);

  if (isMobile) {
    return (
      <MobileItemLayout
        isPaid={false}
        data={lessons}
        totalPrice={`${totalPrice} Kč`}
        totalPaid={renderTotalPaid()}
        renderActionButtons={renderActionButtons}
      />
    );
  }

  return (
    <ItemLayout
      isPaid={false}
      data={lessons}
      totalPrice={`${totalPrice} Kč`}
      totalPaid={renderTotalPaid()}
      renderActionButtons={renderActionButtons}
    />
  );
};

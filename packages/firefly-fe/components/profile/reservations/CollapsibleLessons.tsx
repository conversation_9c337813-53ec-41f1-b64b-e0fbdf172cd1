import {
  Collapse,
  Table,
  TableBody,
  TableCell,
  TableRow,
  Typography,
} from "@mui/material";
import { getFormattedDate } from "../../../helpers/time";
import { AlternateEvent, Event } from "../../../reservanto/types";
import { getTimeFromUnixTimeStamp } from "../../booking/helper";
import { Flex } from "../../commonStyledComponents";
import { CancelBookingButton } from "./CancelBookingButton";

interface Props {
  isCollapse: boolean;
  data?: (Event | AlternateEvent)[];
  onCancel?: (appointmentId: number) => void;
  isPaid: boolean;
  isWholePast?: boolean;
}

export const CollapsibleLessons = ({
  isCollapse,
  data,
  onCancel,
  isPaid,
  isWholePast
}: Props) => {
  if ((data?.length || 0) < 2) {
    return null;
  }
  return (
    <Collapse in={isCollapse} timeout="auto" unmountOnExit>
      <Table sx={{ mt: 1 }}>
        <TableBody>
          {data?.map((reservation) => {
            const reservationDate = getTimeFromUnixTimeStamp(reservation?.StartDate || 0);
            const reservationDateString = getFormattedDate(reservationDate, { year: true }
            );

            const currentDatePlusHour = new Date();
            currentDatePlusHour.setDate(currentDatePlusHour.getDate() + 1);
            const isPast = reservationDate < new Date();
            return (
              <TableRow key={reservation.AppointmentId} sx={{ opacity: isWholePast ? 1 : isPast ? 0.5 : 1 }}>
                <TableCell
                  sx={{
                    width: "21.66%",
                    border: "unset",
                    py: 0.75,
                    fontSize: "0.725rem",
                  }}
                >
                  <Flex alignItems="center">
                    <Typography
                      fontWeight="bold"
                      width={{ xs: "75px", sm: "80px" }}
                      textAlign="right"
                      fontSize="0.725rem"
                    >
                      {reservationDateString}
                    </Typography>
                  </Flex>
                </TableCell>
                <TableCell
                  sx={{
                    width: "25%",
                    border: "unset",
                    py: 0.75,
                    fontSize: "0.7rem",
                  }}
                >
                  {isPaid && onCancel && (
                    <CancelBookingButton
                      appointmentId={reservation.AppointmentId}
                      onCancel={onCancel}
                      appointmentStart={reservation.StartDate}
                      isPaid={(reservation as Event).IsPaid}
                    />
                  )}
                </TableCell>
                <TableCell
                  sx={{
                    width: "50%",
                    border: "unset",
                    py: 0.75,
                    pr: { xs: 0, sm: "inherit" },
                  }}
                ></TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </Collapse>
  );
};

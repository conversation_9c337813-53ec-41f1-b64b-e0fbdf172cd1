import { Button } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import { useDialog } from "../../../providers/DialogProvider";
import { Event } from "../../../reservanto/types";
import { bankInfo, getAmountToPay } from "../../booking/payment/helpers";
import { useBooking } from "../../booking/useBooking";
import { CancelBookingButton } from "./CancelBookingButton";
import {
  getPayButtonText,
  getTotalPaidForCourse,
  getTotalPriceForCourse,
} from "./helpers";
import { ItemLayout } from "./ItemLayout";
import { MobileItemLayout } from "./mobile/MobileItemLayout";
import { getCourseReservationsDetail } from "./query";

interface Props {
  data?: Event[];
  courseId: number;
  isMobile?: boolean;
}

export const ReservationItemCourse = ({
  data: lessons = [],
  courseId,
  isMobile,
}: Props) => {
  const { data } = useQuery(
    ["courseCustomerEvents", { courseId }],
    getCourseReservationsDetail,
    {
      enabled: courseId != null,
    }
  );

  const { cancelBooking } = useBooking();
  const { goTo } = useDialog();

  const sortedLessons = lessons.slice();
  const firstLesson = sortedLessons[0];

  const handleCancel = (appointmentId: number, cancelAllCourse?: boolean) => {
    cancelBooking.mutate({
      appointmentId,
      enableCancelWholeCourse: cancelAllCourse,
    });
  };

  const handlePayment = () => {
    const amount = getAmountToPay(sortedLessons);
    goTo(
      "payment",
      {
        appointmentId: firstLesson?.AppointmentId,
        courseId: firstLesson?.CourseId,
        bookingServiceId: firstLesson?.BookingServiceId,
        payment: {
          amount,
          type: firstLesson?.CalendarName as keyof typeof bankInfo,
        },
      },
      "center"
    );
  };

  const renderActionButtons = () => {
    if (data == null) {
      return null;
    }
    return (
      <>
        {!data?.IsPaid && sortedLessons.length > 0 && (
          <Button
            variant="outlined"
            color="primary"
            size="medium"
            sx={{
              whiteSpace: "nowrap",
            }}
            onClick={handlePayment}
          >
            {getPayButtonText(sortedLessons, data)}
          </Button>
        )}
        <CancelBookingButton
          appointmentId={sortedLessons[0].AppointmentId}
          sx={{ float: "right" }}
          onCancel={handleCancel}
          appointmentStart={sortedLessons[0].StartDate}
          isPaid={!!data?.IsPaid}
          totalPaid={data?.PaidPartOfPriceWithVat}
          size="medium"
          isSpecificLesson={false}
        />
      </>
    );
  };

  if (isMobile) {
    return (
      <MobileItemLayout
        data={sortedLessons}
        isPaid={!!data?.IsPaid}
        totalPaid={getTotalPaidForCourse(data)}
        totalPrice={getTotalPriceForCourse(data)}
        renderActionButtons={renderActionButtons}
        onCancelSingleLesson={handleCancel}
      />
    );
  }

  return (
    <ItemLayout
      data={sortedLessons}
      isPaid={!!data?.IsPaid}
      onCancelSingleLesson={handleCancel}
      renderActionButtons={renderActionButtons}
      totalPaid={getTotalPaidForCourse(data)}
      totalPrice={getTotalPriceForCourse(data)}
    />
  );
};

import { Box, Button, Typography } from "@mui/material";
import { useDialog } from "../../../providers/DialogProvider";
import { Flex } from "../../commonStyledComponents";

export const ConfirmCancelDialog = () => {
  const { dialog } = useDialog();
  return (
    <Flex flexDirection="column" alignItems="center">
      <Typography variant="h2">Zrušen<PERSON></Typography>
      <Typography sx={{ my: 3 }}>
        Opravdu chcete zrušit vaši přihlášku?
      </Typography>
      <Flex gap={2}>
        <Button onClick={dialog.onCancel}>Ponechat přihlášku</Button>
        <Button onClick={dialog.onConfirm} variant="outlined">
          Zrušit přihlášku
        </Button>
      </Flex>
    </Flex>
  );
};

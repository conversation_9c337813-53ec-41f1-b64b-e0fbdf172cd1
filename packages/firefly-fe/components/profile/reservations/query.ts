import { QueryFunctionContext } from "@tanstack/react-query";
import { axiosApi } from "../../../axios";
import {
  AlternateEvent,
  CourseCustomerEvents,
  Event,
} from "../../../reservanto/types";

export const getReservations = async ({
  queryKey,
}: QueryFunctionContext<[string]>): Promise<(Event | AlternateEvent)[]> => {
  const response = await axiosApi({
    method: "get",
    url: "/reservations",
  });

  return response.data;
};

export const getCourseReservationsDetail = async ({
  queryKey,
}: QueryFunctionContext<
  [string, { courseId: number }]
>): Promise<CourseCustomerEvents> => {
  const [_, { courseId }] = queryKey;
  const response = await axiosApi({
    method: "get",
    url: `/courses/${courseId}/customerEvents`,
  });

  return response.data;
};

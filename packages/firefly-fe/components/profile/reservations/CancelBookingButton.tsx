import { Button, IconButton, SxProps, Theme } from "@mui/material";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import { useDialog } from "../../../providers/DialogProvider";

interface Props {
  appointmentStart: number;
  isPaid?: boolean;
  appointmentId: number;
  onCancel: (appointmentId: number, cancelAllCourse?: boolean) => void;
  size?: "small" | "medium";
  isSpecificLesson?: boolean;
  totalPaid?: number;
  isAlternate?: boolean;
  sx?: SxProps<Theme> | undefined;
}

const ONE_WEEK = 604800000;
const ONE_DAY = 86400000;

export const CancelBookingButton = ({
  appointmentStart,
  isPaid,
  onCancel,
  appointmentId,
  isSpecificLesson = true,
  totalPaid,
  size = "small",
  isAlternate,
  sx,
}: Props) => {
  const { goTo, close } = useDialog();
  const currentDatePlusHour = new Date();
  currentDatePlusHour.setDate(currentDatePlusHour.getDate() + 1);
  const isOldReservation =
    currentDatePlusHour.getTime() > appointmentStart * 1000;

  const currentTime = new Date();
  const timeToCompare = appointmentStart * 1000 - currentTime.getTime();

  const confirmCancel = () => {
    onCancel(appointmentId, !isSpecificLesson);
    close();
  };

  if (isAlternate) {
    return (
      <IconButton
        onClick={() =>
          goTo(
            "confirmCancel",
            {
              onConfirm: confirmCancel,
              onCancel: close,
            },
            "center"
          )
        }
        color="primary"
      >
        <CancelOutlinedIcon />
      </IconButton>
    );
  }

  if (!isOldReservation && isPaid && isSpecificLesson) {
    return (
      <Button
        variant="outlined"
        color="primary"
        size={size}
        onClick={() => onCancel(appointmentId)}
        sx={{
          whiteSpace: "nowrap",
          float: { xs: "right", sm: "left" },
          ...sx,
        }}
      >
        Omluvit se
      </Button>
    );
  } else if (totalPaid === 0 && !isSpecificLesson && timeToCompare > ONE_WEEK) {
    return (
      <IconButton
        onClick={() =>
          goTo(
            "confirmCancel",
            {
              onConfirm: confirmCancel,
              onCancel: close,
            },
            "center"
          )
        }
        color="primary"
      >
        <CancelOutlinedIcon />
      </IconButton>
    );
  } else if (
    totalPaid === 0 &&
    !isPaid &&
    isSpecificLesson &&
    timeToCompare > ONE_DAY
  ) {
    return (
      <IconButton
        onClick={() =>
          goTo(
            "confirmCancel",
            {
              onConfirm: confirmCancel,
              onCancel: close,
            },
            "center"
          )
        }
        color="primary"
      >
        <CancelOutlinedIcon />
      </IconButton>
    );
  }

  return null;
};

import { DoubleArrow } from "@mui/icons-material";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, TableCell, TableRow, Typography } from "@mui/material";
import { useState } from "react";
import { getFormattedDate, getFormattedTime } from "../../../helpers/time";
import { AlternateEvent, Event } from "../../../reservanto/types";
import { getTimeFromUnixTimeStamp } from "../../booking/helper";
import { Flex } from "../../commonStyledComponents";
import { CollapsibleLessons } from "./CollapsibleLessons";

interface Props {
  data?: (Event | AlternateEvent)[];
  isPaid?: boolean;
  renderActionButtons?: () => React.ReactNode;
  totalPrice?: string | React.ReactNode;
  totalPaid?: string | React.ReactNode;
  onCancelSingleLesson?: (appointmentId: number) => void;
}

export const ItemLayout = ({
  data: lessons = [],
  isPaid,
  renderActionButtons,
  totalPrice,
  totalPaid,
  onCancelSingleLesson,
}: Props) => {
  const [isCollapse, setIsCollapse] = useState(false);

  const isCourse = lessons.length > 1;

  const now = new Date();
  const futureLessons = lessons.filter(lesson => getTimeFromUnixTimeStamp(lesson?.StartDate || 0) > now);
  let closetLesson = futureLessons.reduce((closest, current) => {
    return current.StartDate < closest.StartDate ? current : closest;
  }, futureLessons[0]);

  if (closetLesson == null) {
    closetLesson = lessons[0]
  }


  const date = getFormattedDate(
    getTimeFromUnixTimeStamp(closetLesson?.StartDate || 0),
    { year: true }
  );
  const time = getFormattedTime(
    getTimeFromUnixTimeStamp(closetLesson?.StartDate || 0)
  );

  const isPast = getTimeFromUnixTimeStamp(closetLesson?.StartDate || 0) < now

  return (
    <>
      <TableRow
        sx={{
          "> td": {
            borderBottom: isCourse ? 0 : undefined,
            opacity: isPast ? 0.4 : 1
          },
        }}
      >
        <TableCell width="22%">
          <Flex alignItems="center">
            <Typography
              width="95px"
              fontWeight="bold"
              textAlign="left"
              fontSize="1rem"
            >
              {date}
            </Typography>
          </Flex>
          <Flex alignItems="center">
            <Typography fontSize="0.75rem">{time}</Typography>
            <Typography ml={2}>{closetLesson.CalendarName}</Typography>
          </Flex>
        </TableCell>
        <TableCell width="20%"><Typography>{closetLesson.ServiceName}</Typography></TableCell>
        <TableCell width="12.5%"><Typography>{totalPrice}</Typography></TableCell>
        <TableCell width="12.5%"><Typography>{totalPaid}</Typography></TableCell>
        <TableCell width="30%" sx={{ textAlign: "right" }}>
          {renderActionButtons?.()}
        </TableCell>
        {!isCourse ? (
          <TableCell width="5%" sx={{ pr: 0 }} />
        ) : (
          <TableCell width="5%" sx={{ pr: 0, borderBottom: "unset" }}>
            <IconButton
              aria-label="expand row"
              size="small"
              onClick={() => setIsCollapse(!isCollapse)}
            >
              {
                <DoubleArrow
                  sx={{
                    transform: `rotate(${isCollapse ? "-90deg" : "90deg"})`,
                    width: "1rem",
                    height: "1rem",
                    color: "primary.main",
                  }}
                />
              }
            </IconButton>
          </TableCell>
        )}
      </TableRow>
      {isCourse && (
        <TableRow sx={{ opacity: isPast ? 0.3 : 1 }}>
          <TableCell
            style={{
              paddingBottom: 0,
              paddingTop: 0,
            }}
            colSpan={6}
          >
            <CollapsibleLessons
              data={lessons}
              isPaid={!!isPaid}
              isWholePast={isPast}
              isCollapse={isCollapse}
              onCancel={onCancelSingleLesson}
            />
          </TableCell>
        </TableRow>
      )}
    </>
  );
};

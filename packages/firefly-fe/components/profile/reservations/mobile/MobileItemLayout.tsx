import { DoubleArrow } from "@mui/icons-material";
import { Box, Divider, IconButton, Paper, Typography } from "@mui/material";
import { useState } from "react";
import { getFormattedDate, getFormattedTime } from "../../../../helpers/time";
import { AlternateEvent, Event } from "../../../../reservanto/types";
import { getTimeFromUnixTimeStamp } from "../../../booking/helper";
import { Flex } from "../../../commonStyledComponents";
import { CollapsibleLessons } from "../CollapsibleLessons";

interface Props {
  data?: (Event | AlternateEvent)[];
  totalPrice: string | React.ReactNode;
  totalPaid: string | React.ReactNode;
  isPaid?: boolean;
  renderActionButtons?: () => React.ReactNode;
  onCancelSingleLesson?: (appointmentId: number) => void;
}

export const MobileItemLayout = ({
  data: lessons = [],
  totalPrice,
  totalPaid,
  isPaid,
  renderActionButtons,
  onCancelSingleLesson,
}: Props) => {
  const [isCollapse, setIsCollapse] = useState(false);

  const now = new Date();
  const futureLessons = lessons.filter(lesson => getTimeFromUnixTimeStamp(lesson?.StartDate || 0) > now);
  let closetLesson = futureLessons.reduce((closest, current) => {
    return current.StartDate < closest.StartDate ? current : closest;
  }, futureLessons[0]);

  if (closetLesson == null) {
    closetLesson = lessons[0]
  }

  const lesson = lessons[0];


  const isCourse = lessons.length > 1;
  const date = getFormattedDate(
    getTimeFromUnixTimeStamp(closetLesson?.StartDate || 0),
    { year: true }
  );
  const time = getFormattedTime(
    getTimeFromUnixTimeStamp(closetLesson?.StartDate || 0)
  );

  const isPast = getTimeFromUnixTimeStamp(closetLesson?.StartDate || 0) < now

  return (
    <Paper sx={{ mb: 2, opacity: isPast ? 0.5 : 1 }}>
      <Flex flexDirection="column">
        <Typography fontSize="0.875rem">Příští lekce</Typography>
        <Flex alignItems="center">
          <Box mr={2} width={90}>
            <Typography fontWeight="bold" fontSize="1rem">
              {date}
            </Typography>
          </Box>
          <Typography fontSize="0.75rem" width={50}>
            {time}
          </Typography>
          <Typography fontSize="0.75rem">{lesson.CalendarName}</Typography>
        </Flex>
      </Flex>
      <Divider sx={{ mx: { xs: -1, md: -3 }, my: 1 }} />
      <Flex justifyContent="space-between">
        <Box>
          <Typography fontSize="0.875rem" mb={1}>
            Lekce / kurz
          </Typography>
          <Typography fontSize="1rem">{lesson?.ServiceName}</Typography>
        </Box>
        {isCourse && (
          <IconButton
            aria-label="expand row"
            size="small"
            onClick={() => setIsCollapse(!isCollapse)}
            sx={{ mr: -1, alignSelf: "center" }}
          >
            {
              <DoubleArrow
                sx={{
                  transform: `rotate(${isCollapse ? "-90deg" : "90deg"})`,
                  width: "1.5rem",
                  height: "1.5rem",
                  color: "primary.main",
                }}
              />
            }
          </IconButton>
        )}
      </Flex>
      {isCourse && (
        <CollapsibleLessons
          data={lessons}
          isPaid={!!isPaid}
          isCollapse={isCollapse}
          onCancel={onCancelSingleLesson}
          isWholePast={isPast}
        />
      )}
      <Divider sx={{ mx: { xs: -1, md: -3 }, my: 1 }} />
      <Flex justifyContent="space-between" alignItems="center" flexWrap="wrap">
        <Flex>
          <Box width={80}>
            <Typography fontSize="0.875rem" mb={1}>
              Cena
            </Typography>
            <Typography fontSize="1rem">{totalPrice}</Typography>
          </Box>
          <Box ml={1}>
            <Typography fontSize="0.875rem" mb={1}>
              Uhrazeno
            </Typography>
            <Typography fontSize="1rem">{totalPaid}</Typography>
          </Box>
        </Flex>
        <Flex alignItems="center" mt={1}>
          {renderActionButtons?.()}
        </Flex>
      </Flex>
    </Paper>
  );
};

import { Box, Collapse } from "@mui/material";
import { AlternateEvent, Event } from "../../../../reservanto/types";
import { AlternateItemCourse } from "../AlternateItemCourse";
import { AlternateItemLesson } from "../AlternateItemLesson";
import { isAlternate, isCourse } from "../helpers";
import { ReservationItemCourse } from "../ReservationItemCourse";
import { ReservationItemLesson } from "../ReservationItemLesson";

interface Props {
  data: { [key: string]: (Event | AlternateEvent)[] };
  showAll: boolean;
}

export const SHOW_AT_LEAST = 3;

export const MobileReservations = ({ data, showAll }: Props) => {
  const sortedData = Object.keys(data).sort(
    (a, b) =>
      data[b][data[b].length ? data[b].length - 1 : 0].StartDate -
      data[a][data[a].length ? data[a].length - 1 : 0].StartDate
  );

  const renderItem = (data: (AlternateEvent | Event)[], key: string) => {
    if (isCourse(data)) {
      if (isAlternate(data)) {
        return (
          <AlternateItemCourse
            courseId={data[0]?.CourseId!}
            data={data as AlternateEvent[]}
            isMobile
            key={key}
          />
        );
      }
      return (
        <ReservationItemCourse
          data={data as Event[]}
          isMobile
          key={key}
          courseId={data[0]?.CourseId!}
        />
      );
    }

    if (isAlternate(data)) {
      return (
        <AlternateItemLesson
          data={data as AlternateEvent[]}
          key={key}
          isMobile
        />
      );
    }
    return <ReservationItemLesson data={data as Event[]} key={key} isMobile />;
  };

  return (
    <Box>
      {sortedData
        ?.slice(0, SHOW_AT_LEAST)
        ?.map((key) => renderItem(data[key], key))}
      <Collapse in={showAll} timeout="auto" unmountOnExit>
        {sortedData
          ?.slice(SHOW_AT_LEAST, sortedData.length || 0)
          .map((key) => renderItem(data[key], key))}
      </Collapse>
    </Box>
  );
};

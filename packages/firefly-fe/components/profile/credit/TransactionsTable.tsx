import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
} from "@mui/material";
import { groupBy } from "rambda";
import { CreditTransactions } from "../../../reservanto/types";
import { TransactionItem } from "./TransactionItem";

interface Props {
  data?: CreditTransactions;
}

export const TransactionsTable = ({ data }: Props) => {
  const transactionsByTime = groupBy(
    (item) =>
      `${new Date(item.CreatedAt * 1000).getFullYear()}-${new Date(
        item.CreatedAt * 1000
      ).getMonth()}-${new Date(item.CreatedAt * 1000).getDay()}-${new Date(
        item.CreatedAt * 1000
      ).getHours()}-${item.Note}`,
    data?.Transactions || []
  );

  const orderedTransactionKeys = Object.keys(transactionsByTime).sort(
    (trans1, trans2) =>
      Number(transactionsByTime[trans2][0].CreatedAt) -
      Number(transactionsByTime[trans1][0].CreatedAt)
  );

  return (
    <Table>
      <TableHead>
        <TableRow>
          <TableCell width="20%">Datum</TableCell>
          <TableCell align="right" width="30%">
            Částka
          </TableCell>
          <TableCell
            width="10%"
            sx={{ display: { xs: "none", sm: "table-cell" } }}
          />
          <TableCell width="50%">Poznámka</TableCell>
        </TableRow>
      </TableHead>
      <TableBody>
        {orderedTransactionKeys.map((timeKey) => (
          <TransactionItem data={transactionsByTime[timeKey]} key={timeKey} />
        ))}
      </TableBody>
    </Table>
  );
};

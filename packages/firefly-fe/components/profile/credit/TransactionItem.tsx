import { TableCell, TableRow, Typography } from "@mui/material";
import { getFormattedDate, getFormattedTime } from "../../../helpers/time";
import { Transaction } from "../../../reservanto/types";
import { getTimeFromUnixTimeStamp } from "../../booking/helper";
import { Flex } from "../../commonStyledComponents";

interface Props {
  data: Transaction[];
}

export const TransactionItem = ({ data }: Props) => {
  const firstTrans = data[0];

  const isIncome = (firstTrans?.Value || 0) > 0;

  const time = getFormattedTime(
    getTimeFromUnixTimeStamp(firstTrans.CreatedAt || 0)
  );
  const date = getFormattedDate(
    getTimeFromUnixTimeStamp(firstTrans.CreatedAt || 0),
    {
      year: true,
    }
  );

  const totalValue = data.reduce((prev, trans) => trans.Value + prev, 0);

  return (
    <TableRow>
      <TableCell width="20%">
        <Flex alignItems="center">
          <Typography width="80px" fontWeight="bold" textAlign="right">
            {date}
          </Typography>
          <Typography fontSize="0.75rem" ml={{ xs: 2, sm: 3 }}>
            {time}
          </Typography>
        </Flex>
      </TableCell>
      <TableCell
        width="30%"
        align="right"
        sx={{
          color: !isIncome ? "#E75292" : "#39CE9C",
        }}
      >
        {isIncome ? "+" : ""}
        {Math.ceil(totalValue)} Kč
      </TableCell>
      <TableCell
        width="10%"
        sx={{ display: { xs: "none", sm: "table-cell" } }}
      />
      <TableCell width="50%">{firstTrans.Note}</TableCell>
    </TableRow>
  );
};

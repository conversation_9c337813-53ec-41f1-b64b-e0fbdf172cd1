import { QueryFunctionContext } from "@tanstack/react-query";
import { axiosApi } from "../../../axios";
import { CreditTransactions } from "../../../reservanto/types";

export const getCreditTransactions = async (
  _: QueryFunctionContext<[string]>
): Promise<CreditTransactions> => {
  const response = await axiosApi({
    method: "get",
    url: "/me/creditTransactions",
  });
  return response.data;
};

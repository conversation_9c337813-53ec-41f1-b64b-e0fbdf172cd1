import { <PERSON>, Button, Collapse, Paper, Typography } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import { useState } from "react";
import { useDialog } from "../../../providers/DialogProvider";
import { Customer } from "../../../reservanto/types";
import { Flex } from "../../commonStyledComponents";
import { ShowMoreButton } from "../../ShowMoreButton";
import { getCreditTransactions } from "./query";
import { TransactionsTable } from "./TransactionsTable";

interface Props {
  data?: Customer;
}

export const Credit = ({ data }: Props) => {
  const [showMore, setShowMore] = useState(false);

  const { goTo } = useDialog();
  const { data: creditTransactions } = useQuery(
    ["creditTransactions"],
    getCreditTransactions,
    {
      enabled: !!data?.Id,
    }
  );

  const handleCharge = () => {
    goTo(
      "payment",
      {
        payment: {
          type: "credit",
        },
      },
      "center"
    );
  };

  return (
    <Box mt={5} width="100%">
      <Typography variant="h4">Kredit</Typography>
      <Paper sx={{ mt: 2 }}>
        <Typography>Aktuální kredit</Typography>
        <Flex mt={2}>
          <Flex flex={1}>
            <Typography fontSize="1.75rem" fontWeight="bold">
              {data?.Credit} Kč
            </Typography>
            <Button variant="outlined" sx={{ ml: 2 }} onClick={handleCharge}>
              Dobít kredit
            </Button>
          </Flex>
          {!!creditTransactions?.Transactions.length && (
            <ShowMoreButton
              showMore={showMore}
              setShowMore={setShowMore}
              sx={{ display: { xs: "none", sm: "flex" } }}
            >
              Historie plateb
            </ShowMoreButton>
          )}
          {!!creditTransactions?.Transactions.length && (
            <ShowMoreButton
              showMore={showMore}
              setShowMore={setShowMore}
              sx={{ display: { xs: "flex", sm: "none" } }}
            />
          )}
        </Flex>
        {!!creditTransactions?.Transactions.length && (
          <Collapse in={showMore} timeout="auto" unmountOnExit>
            <Box pt={4}>
              <TransactionsTable data={creditTransactions} />
            </Box>
          </Collapse>
        )}
      </Paper>
    </Box>
  );
};

import * as yup from "yup";

export const editingSchema = yup
  .object()
  .shape({
    name: yup.string().required("Toto pole je požadované"),
    email: yup
      .string()
      .required("Toto pole je požadované")
      .matches(
        /^\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/,
        "Špatný formát"
      ),
    phone: yup
      .string()
      .required("Toto pole je požadované")
      .matches(/^[+]*[(]{0,1}[0-9]{1,4}[)]{0,1}[-\s\./0-9]*$/, "Špatný formát"),
    password: yup
      .string()
      .matches(
        /(^$|^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9]).{6,}$)/,
        "Heslo musí obsahovat nejméně 6 zna<PERSON><PERSON>, j<PERSON><PERSON>, jeden velk<PERSON> znak a jeden malý."
      ),
    passwordAgain: yup
      .string()
      .when("password", {
        is: (password: string) => password.length > 0,
        then: yup.string().required("Toto pole je požadované"),
      })
      .oneOf([yup.ref("password"), null], "Heslo se neshoduje"),
  })
  .required("Toto pole je požadované");

export const creatingSchema = yup
  .object()
  .shape({
    name: yup.string().required("Toto pole je požadované"),
    email: yup
      .string()
      .required("Toto pole je požadované")
      .matches(
        /^\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/,
        "Špatný formát"
      ),
    phone: yup
      .string()
      .required("Toto pole je požadované")
      .matches(/^[+]*[(]{0,1}[0-9]{1,4}[)]{0,1}[-\s\./0-9]*$/, "Špatný formát"),
    password: yup
      .string()
      .matches(
        /^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9]).{6,}$/,
        "Heslo musí obsahovat nejméně 6 znaků, jedno číslo, jeden velký znak a jeden malý."
      )
      .required("Toto pole je požadované"),
    passwordAgain: yup
      .string()
      .required("Toto pole je požadované")
      .oneOf([yup.ref("password"), null], "Heslo se neshoduje"),
  })
  .required("Toto pole je požadované");

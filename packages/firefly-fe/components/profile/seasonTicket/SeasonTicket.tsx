import {
  Box,
  Link,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Typography
} from "@mui/material";
import { getFormattedDate } from "../../../helpers/time";
import { Customer } from "../../../reservanto/types";
import { getTimeFromUnixTimeStamp } from "../../booking/helper";

interface Props {
  data?: Customer;
}

export const SeasonTicket = ({ data }: Props) => {
  const pass = data?.BoughtPasses?.[0];

  return (
    <Box mt={5} width="100%">
      <Typography variant="h4">Permanentka</Typography>
      {pass != null && (
        <Paper sx={{ mt: 2 }}>
          <Box>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell sx={{ width: { xs: "70%", sm: "80%" } }}>
                    Název
                  </TableCell>
                  <TableCell sx={{ width: { xs: "30%", sm: "20%" } }}>
                    Platnost do
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {data?.BoughtPasses?.map((pass) => (
                  <TableRow key={pass.PassId}>
                    <TableCell>
                      <b>{pass.PassName}</b>
                    </TableCell>
                    <TableCell>
                      {getFormattedDate(
                        getTimeFromUnixTimeStamp(pass?.ExpirationDate || 0),
                        { year: true }
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </Box>
        </Paper>
      )}
      <Typography mt={3} maxWidth={550}>
        Pokud máš zájem o permanentku na Open space či letní Open class permanentku, oslov některou z našich recepčních ve studiu nebo nám napiš na{" "}
        <Link href="mailto: <EMAIL>">
          <EMAIL>
        </Link>
      </Typography>
    </Box>
  );
};

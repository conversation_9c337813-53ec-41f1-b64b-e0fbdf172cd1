import { Box, Button, Typography } from "@mui/material";
import { useState } from "react";
import { useMe } from "../../providers/MeProvider";
import { Customer } from "../../reservanto/types";
import { Flex } from "../commonStyledComponents";
import { ProfileInfoForm, ProfileInfoFormValues } from "./ProfileInfoForm";

interface Props {
  data?: Customer;
}

export const ProfileInfo = ({ data }: Props) => {
  const [isEditing, setIsEditing] = useState(false);
  const { updateMe } = useMe();

  const handleSaveProfile = (values: ProfileInfoFormValues) => {
    if (values.password === "") {
      delete values.password;
    }

    updateMe?.mutate(values, {
      onSuccess: () => {
        setIsEditing(false);
      },
    });
  };

  return (
    <Box width="100%">
      {!isEditing ? (
        <>
          <Flex alignItems="center">
            <Typography variant="h4">{data?.Name || "Profil"}</Typography>
            <Button
              variant="outlined"
              sx={{
                ml: 3,
                display: { xs: "none", sm: "block" },
              }}
              onClick={() => setIsEditing(true)}
              color="primary"
              size="medium"
            >
              Upravit
            </Button>
          </Flex>
          <Flex
            width="100%"
            justifyContent="space-between"
            alignItems="center"
            mt={2}
          >
            <Box>
              <Typography>{data?.Email}</Typography>
              <Typography sx={{ mt: 1 }}>{data?.Phone}</Typography>
            </Box>
            <Button
              variant="outlined"
              sx={{
                ml: 3,
                display: { sm: "none" },
              }}
              onClick={() => setIsEditing(true)}
              color="primary"
              size="medium"
            >
              Upravit
            </Button>
          </Flex>
        </>
      ) : (
        <ProfileInfoForm
          onSubmit={handleSaveProfile}
          isEditing
          onCancelEdit={() => setIsEditing(false)}
          defaultValues={{
            name: data?.Name || "",
            email: data?.Email || "",
            phone: data?.Phone || "",
            password: "",
          }}
          renderSubmitButton={() => (
            <Button type="submit" color="primary" variant="outlined">
              Uložit
            </Button>
          )}
        />
      )}
    </Box>
  );
};

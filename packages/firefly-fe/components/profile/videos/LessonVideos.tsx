import { <PERSON>, But<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Typography } from "@mui/material";
import { useState } from "react";
import { Lesson } from "../../../strapi/types";
import { Flex } from "../../commonStyledComponents";
import Link from "../../Link";
import { ShowMoreButton } from "../../ShowMoreButton";

interface Props {
  item: Lesson;
}

export const LessonVideos = ({ item }: Props) => {
  const [showMore, setShowMore] = useState(false);

  if (item.videos.length < 1) {
    return null;
  }

  return (
    <>
      <Flex justifyContent="space-between" alignItems="center">
        <Typography fontWeight={900}>{item.name}</Typography>
        <ShowMoreButton showMore={showMore} setShowMore={setShowMore} />
      </Flex>
      <Divider sx={{ my: 1 }} />
      <Collapse in={showMore} timeout="auto" unmountOnExit>
        <Box mt={2}>
          {item.videos.map((video) => (
            <Flex key={video.id} alignItems="center" my={1}>
              <Typography sx={{ width: { xs: "25%", sm: "10%" } }}>
                {video.name}
              </Typography>
              <Typography sx={{ width: { xs: "25%", sm: "10%" } }}>
                {video.duration}
              </Typography>
              <Typography width="30%">{video.description}</Typography>
              <Button
                color="primary"
                variant="outlined"
                component={Link}
                target="_blank"
                href={video.url}
              >
                Spustit
              </Button>
            </Flex>
          ))}
        </Box>
      </Collapse>
    </>
  );
};

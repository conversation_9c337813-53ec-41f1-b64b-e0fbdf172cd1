import { QueryFunctionContext } from "@tanstack/react-query";
import { axiosCms, StrapiArrayResponse } from "../../../axios";
import { Lesson } from "../../../strapi/types";

export const getLesson = async ({
  queryKey,
}: QueryFunctionContext<[string, { bookingServiceIds: number[] }]>): Promise<
  StrapiArrayResponse<Lesson>
> => {
  const [, { bookingServiceIds }] = queryKey;
  const response = await axiosCms.get(`/lessons`, {
    params: {
      "filters[bookingServiceId][$in]": bookingServiceIds,
      populate: "deep,4",
    },
  });
  return response.data;
};

import { Box, Divider, Paper, Typography } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import { uniq } from "rambda";
import { LessonVideos } from "./LessonVideos";
import { getLesson } from "./query";

interface Props {
  bookingServiceIds?: number[];
}

export const Videos = ({ bookingServiceIds }: Props) => {
  const { data } = useQuery(
    ["lessonVideos", { bookingServiceIds: uniq(bookingServiceIds || [])! }],
    getLesson,
    {
      enabled: (bookingServiceIds?.length || 0) > 0,
    }
  );

  const someVideo = data?.data.find(
    (item) => item.attributes.videos.length > 0
  );

  if (!someVideo) {
    return null;
  }

  return (
    <Box mt={5} width="100%">
      <Typography variant="h4">Video lekce</Typography>
      <Paper sx={{ mt: 2 }}>
        <Typography><PERSON><PERSON><PERSON><PERSON> kurzy</Typography>
        <Divider sx={{ my: 2 }} />
        {data?.data.map((item) => (
          <LessonVideos
            item={item.attributes}
            key={item.attributes.bookingServiceId}
          />
        ))}
      </Paper>
    </Box>
  );
};

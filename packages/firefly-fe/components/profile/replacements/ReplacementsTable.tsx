import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow
} from "@mui/material";
import { Replacement } from "../types";
import { ReplacementItem } from "./ReplacementItem";

interface Props {
  data?: Replacement[];
}

export const ReplacementsTable = ({ data }: Props) => {
  return (
    <Table>
      <TableHead>
        <TableRow>
          <TableCell width="20%">Za co vznikla</TableCell>
          <TableCell sx={{ width: { xs: "50%", sm: "60%" } }}>
            Možno použít na
          </TableCell>
          <TableCell sx={{ width: { xs: "30%", sm: "20%" } }}>
            Platnost do
          </TableCell>
        </TableRow>
      </TableHead>
      <TableBody>
        {data?.map((item) => (
          <ReplacementItem key={item.Id} data={item} />
        ))}
      </TableBody>
    </Table>
  );
};

import { TableCell, TableRow, Typography } from "@mui/material";
import { getFormattedDate } from "../../../helpers/time";
import { getTimeFromUnixTimeStamp } from "../../booking/helper";
import { Replacement } from "../types";

interface Props {
  data?: Replacement;
}

export const ReplacementItem = ({ data }: Props) => {
  return (
    <TableRow>
      <TableCell width="20%">
        <Typography fontWeight="bold">
          {data?.SourceBookingServiceName}
        </Typography>
      </TableCell>
      <TableCell width="50%">
        {data?.Services.map((s) => s.Name).join(", ")}
      </TableCell>
      <TableCell width="30%">
        <Typography>
          {data?.ExpirationDate &&
            getFormattedDate(getTimeFromUnixTimeStamp(data?.ExpirationDate), {
              year: true,
            })}
        </Typography>
      </TableCell>
    </TableRow>
  );
};

import { Box, Paper, Typography } from "@mui/material";
import { Customer } from "../../../reservanto/types";
import { ReplacementsTable } from "./ReplacementsTable";
import { useReplacements } from "./useReplacements";

interface Props {
  data?: Customer;
}

export const Replacements = ({ data }: Props) => {
  const {
    replacementsQuery: { data: replacements },
  } = useReplacements();
  
  return (
    <Box mt={5} width="100%">
      <Typography variant="h4">Náhrady</Typography>
      <Paper sx={{ mt: 2 }}>
        {(replacements?.length || 0) > 0 ? (
          <ReplacementsTable data={replacements} />
        ) : (
          <Typography><PERSON><PERSON><PERSON><PERSON> n<PERSON>dy.</Typography>
        )}
      </Paper>
    </Box>
  );
};

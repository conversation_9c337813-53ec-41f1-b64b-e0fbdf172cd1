import { useQuery } from "@tanstack/react-query";
import { getClassesForLesson } from "../../activityDetail/query";
import { getReplacements } from "./query";

interface Props {
  bookingServiceId?: number;
  courseId?: number | null;
}

interface Options {
  canApplyReplacement?: boolean;
}

export const useReplacements = (props?: Props, options?: Options) => {
  const { data: lessonClasses } = useQuery(
    ["classesForLesson", { bookingServiceId: props?.bookingServiceId! }],
    getClassesForLesson,
    {
      enabled: options?.canApplyReplacement,
    }
  );

  let correctLessonClasses = lessonClasses;

  if (props?.courseId) {
    correctLessonClasses = correctLessonClasses?.filter(
      (l) => l.CourseId === props.courseId
    );
  }

  const bookingServiceId = props?.bookingServiceId;
  const replacementsQuery = useQuery(["replacements"], getReplacements);

  let canApplyReplacement: boolean = false;

  if (bookingServiceId && props?.courseId == null && (correctLessonClasses?.length || 0) > 2) {
    replacementsQuery.data?.forEach((replacement) => {
      if (!canApplyReplacement) {
        const foundService = replacement.Services.find(
          (service) => service.Id === bookingServiceId
        );
        if (foundService) {
          canApplyReplacement = true;
        }
      }
    });
  }

  return { replacementsQuery, canApplyReplacement };
};

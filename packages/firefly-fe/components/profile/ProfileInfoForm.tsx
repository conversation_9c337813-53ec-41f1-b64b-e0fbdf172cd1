import { Controller, useForm } from "react-hook-form";
import { Flex } from "../commonStyledComponents";
import { yupResolver } from "@hookform/resolvers/yup";
import { TextInput } from "../form/TextInput";
import { creatingSchema, editingSchema } from "./formValidSchema";
import { Button, useTheme } from "@mui/material";

interface Props {
  onSubmit: (values: ProfileInfoFormValues) => void;
  renderSubmitButton: () => React.ReactNode;
  defaultValues?: ProfileInfoFormValues;
  isEditing?: boolean;
  onCancelEdit?: () => void;
}

export interface ProfileInfoFormValues {
  name: string;
  email: string;
  phone: string;
  password?: string;
  passwordAgain?: string;
}

export const ProfileInfoForm = ({
  onSubmit,
  renderSubmitButton,
  defaultValues,
  isEditing,
  onCancelEdit,
}: Props) => {
  const {
    handleSubmit,
    control,
    formState: { errors, dirtyFields },
  } = useForm<ProfileInfoFormValues>({
    resolver: yupResolver(isEditing ? editingSchema : creatingSchema),
    defaultValues,
  });
  const { palette } = useTheme();
  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Flex flexDirection="column" maxWidth={400}>
        <Controller
          control={control}
          name="name"
          render={({ field }) => (
            <TextInput
              {...field}
              variant="outlined"
              placeholder="Jméno a příjmení"
              sx={{ marginBottom: 2 }}
              helperText={errors.name?.message}
              FormHelperTextProps={{
                sx: {
                  color: `${isEditing ? palette.error.light : palette.common.white} !important`,
                },
              }}
            />
          )}
        />
        <Controller
          control={control}
          name="email"
          render={({ field }) => (
            <TextInput
              {...field}
              variant="outlined"
              placeholder="E-mail"
              type="email"
              sx={{ marginBottom: 2 }}
              helperText={errors.email?.message}
              FormHelperTextProps={{
                sx: {
                   color: `${isEditing ? palette.error.light : palette.common.white} !important`,
                },
              }}
            />
          )}
        />
        <Controller
          control={control}
          name="phone"
          render={({ field }) => (
            <TextInput
              {...field}
              variant="outlined"
              placeholder="Telefon"
              sx={{ marginBottom: 2 }}
              helperText={errors.phone?.message}
              inputRegex={/^[+\d\s]+$/}
              FormHelperTextProps={{
                sx: {
                   color: `${isEditing ? palette.error.light : palette.common.white} !important`,
                },
              }}
            />
          )}
        />
        <Controller
          control={control}
          name="password"
          render={({ field }) => (
            <TextInput
              {...field}
              variant="outlined"
              placeholder={isEditing ? "Nové heslo" : "Heslo"}
              type="password"
              sx={{ marginBottom: 2 }}
              helperText={errors.password?.message}
              FormHelperTextProps={{
                sx: {
                   color: `${isEditing ? palette.error.light : palette.common.white} !important`,
                },
              }}
            />
          )}
        />
        <Controller
          control={control}
          name="passwordAgain"
          render={({ field }) => (
            <TextInput
              {...field}
              variant="outlined"
              placeholder={isEditing ? "Potvrzení nového hesla" : "Heslo znovu"}
              type="password"
              sx={{ marginBottom: 3 }}
              helperText={errors.passwordAgain?.message}
              FormHelperTextProps={{
                sx: {
                   color: `${isEditing ? palette.error.light : palette.common.white} !important`,
                },
              }}
            />
          )}
        />
        {isEditing && Object.keys(dirtyFields).length < 1 ? (
          <Button color="primary" variant="outlined" onClick={onCancelEdit}>
            Zrušit
          </Button>
        ) : (
          renderSubmitButton()
        )}
      </Flex>
    </form>
  );
};

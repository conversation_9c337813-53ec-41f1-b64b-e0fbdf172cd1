import { StrapiArrayResponse, StrapiResponse } from "../../axios";
import { Lector } from "../../strapi/types";

export interface ActivityResponse {
  title: string;
  description: string;
  tryItText: string;
  news: string;
  image: StrapiResponse<{
    alternativeText: string;
    url: string;
  }>;
  slug: string;
  lesson_types: StrapiArrayResponse<{
    title: string;
    description: string;
    type: "privateLesson" | "openSpace" | "course" | "openClass";
  }>;
  lectors: StrapiArrayResponse<Lector>;
}

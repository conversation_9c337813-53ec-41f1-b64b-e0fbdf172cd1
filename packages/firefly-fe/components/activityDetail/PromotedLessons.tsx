import { DoubleArrow } from "@mui/icons-material";
import { Box, Typography } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import { routes } from "../../routes";
import Link from "../Link";
import Calendar from "./Calendar";
import { getClassesForLesson } from "./query";
import { groupBy } from "rambda";
import { Class } from "../../reservanto/types";

interface Props {
  bookingServiceId?: number;
  activityId?: number;
}

const PromotedLessons = ({ bookingServiceId, activityId }: Props) => {
  const { data: lessonClasses, isLoading } = useQuery(
    ["classesForLesson", { bookingServiceId: bookingServiceId! }],
    getClassesForLesson,
    {
      enabled: !!bookingServiceId,
    }
  );

  const lessonClassesGroupedByUniqueId = groupBy(
    (c) => c.CourseId?.toString() || c.BookingServiceId.toString(),
    lessonClasses || []
  );

  let uniqueClasses: Class[] = [];
  Object.values(lessonClassesGroupedByUniqueId).forEach((lessons) => {
    if (lessons[0].CourseId) {
      const isFull = lessons.find(
        (lesson) => lesson.Capacity === lesson.OccupiedCapacity
      );
      if (isFull || lessons.length < 10) {
        return;
      }
      uniqueClasses.push(lessons[0]);
    } else {
      uniqueClasses.push(...lessons);
    }
  });

  return (
    <>
      <Typography fontSize="1rem" fontWeight={700}>
        Vypsané lekce
      </Typography>
      <Box mt={1}>
        <Calendar data={uniqueClasses} isLoading={isLoading} />
      </Box>
      <Link
        sx={{
          mt: 4,
          fontWeight: 900,
          fontSize: "1.1rem",
          fontFamily: "HrotBasic",
        }}
        href={`${routes.booking}?activityIds=${activityId}`}
        endIcon={<DoubleArrow />}
      >
        Rozvrh
      </Link>
    </>
  );
};

export default PromotedLessons;

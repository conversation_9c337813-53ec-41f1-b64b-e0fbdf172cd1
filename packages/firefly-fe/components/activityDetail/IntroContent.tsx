import DoubleArrowIcon from "@mui/icons-material/DoubleArrow";
import { Box, styled, Typography } from "@mui/material";
import Image from "next/image";
import { animated } from "react-spring";
import { getImageUrl } from "../../helpers/image";
import { routes } from "../../routes";
import AnimatingBlock from "../animatingBlock/AnimatingBlock";
import { Flex } from "../commonStyledComponents";
import Link from "../Link";
import { ActivityResponse } from "./types";

interface Props {
  data?: ActivityResponse;
  activityId?: number;
}

const IntroContent = ({ data, activityId }: Props) => {
  return (
    <>
      <Flex
        alignItems="flex-start"
        flexDirection="column"
        justifyContent="center"
        position={{ xs: "relative", sm: "initial" }}
        minWidth={{ sm: 300 }}
        mx={{ xs: 5, sm: 10 }}
        mt={{ xs: 5, sm: 0 }}
      >
        <Typography
          variant="h1"
          color="background.default"
          textAlign="start"
          py={{ xs: 3, sm: 0 }}
        >
          {data?.title}
        </Typography>
        <Box sx={{ mt: { xs: 10, sm: 6 }, mb: { xs: 10, sm: 0 } }}>
          <Typography maxWidth={350}>{data?.description}</Typography>
        </Box>
        {data?.image.data.attributes.url && (
          <BackgroundImage>
            <Image
              src={getImageUrl(data.image.data.attributes.url)}
              alt={data.title}
              objectFit="cover"
              layout="fill"
              priority
            />
          </BackgroundImage>
        )}
      </Flex>
      <Flex
        alignSelf="flex-end"
        position="relative"
        id="lessonTypes"
        flexDirection={{ xs: "column", sm: "row" }}
        height="100%"
        ml={{ xs: 0, sm: 12 }}
        zIndex={5}
      >
        {data?.lesson_types?.data.map((l, index) => (
          <AnimatingBlock
            width={260}
            percentageHeight={45}
            animatedContentHeight={55}
            key={l.id}
            disableAnimationOnMobile
            index={index}
            render={({ zoomedContentStyle }) => (
              <Flex flexDirection="column" height="100%">
                <Box p={3}>
                  <Typography variant="h4" width={{ xs: "100%", sm: 215 }}>
                    {l?.attributes?.title}
                  </Typography>
                  <Typography mt={3} width={{ xs: "100%", sm: 215 }}>
                    {l?.attributes?.description}
                  </Typography>
                  {l?.attributes?.type !== "privateLesson" && (
                    <Link
                      sx={{ mt: 3, display: { xs: "flex", sm: "none" } }}
                      href={`${routes.booking}?lessonTypeIds=${l.id}&activityIds=${activityId}`}
                      endIcon={<DoubleArrowIcon />}
                    >
                      Rozvrh
                    </Link>
                  )}
                  {l?.attributes?.type !== "privateLesson" && (
                    <Box sx={{ display: { xs: "none", sm: "block" } }}>
                      <animated.div style={zoomedContentStyle}>
                        <Link
                          sx={{ mt: 3 }}
                          href={`${routes.booking}?lessonTypeIds=${l.id}&activityIds=${activityId}`}
                          endIcon={<DoubleArrowIcon />}
                        >
                          Rozvrh
                        </Link>
                      </animated.div>
                    </Box>
                  )}
                </Box>
              </Flex>
            )}
          />
        ))}
      </Flex>
    </>
  );
};

export default IntroContent;

const BackgroundImage = styled(Box)`
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  max-width: 100vh;
  filter: saturate(0);
  z-index: -1;

  ${(props) => props.theme.breakpoints.down("md")} {
    top: -40px;
    min-height: 100%;
    width: auto;
    left: -40px;
    right: -40px;
    bottom: -40px;
  }
`;

import { DoubleArrow } from "@mui/icons-material";
import { Box, styled, Typography } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import Image from "next/image";
import { animated } from "react-spring";
import { getImageUrl } from "../../helpers/image";
import { routes } from "../../routes";
import AnimatingBlock from "../animatingBlock/AnimatingBlock";
import { Flex } from "../commonStyledComponents";
import Link from "../Link";
import PromotedLessons from "./PromotedLessons";
import { getPromotedLesson } from "./query";
import { ActivityResponse } from "./types";
import { ClipboardFeature } from "../ClipboardFeature";
import { slugify } from "../../helpers/url";

interface Props {
  data?: ActivityResponse;
  activityId?: number;
}

const TryItContent = ({ data, activityId }: Props) => {
  const { data: promotedLessonData } = useQuery(
    ["promotedLesson", { activityId: activityId! }],
    getPromotedLesson,
    {
      enabled: !!activityId,
    }
  );

  const promotedLessons = promotedLessonData?.data;

  const orderedLectors = data?.lectors?.data.sort(
    (a, b) => (a?.attributes?.order || 1000) - (b?.attributes?.order || 1000)
  );

  return (
    <>
      {promotedLessons?.map((promotedLesson) => (
        <Flex
          alignItems="flex-start"
          key={promotedLesson.id}
          flexDirection="column"
          justifyContent="center"
          id={slugify(promotedLesson.attributes.name)}
          minWidth={{ xs: "auto", sm: 500 }}
          flexShrink={0}
          mx={{ xs: 5, sm: 10 }}
          mt={{ xs: 5, sm: 0 }}
        >
          <Box maxWidth={500}>
            <Typography variant="h1" textAlign="start">
              <ClipboardFeature
                anchor={slugify(promotedLesson.attributes.name)}
              >
                {promotedLesson?.attributes.name}
              </ClipboardFeature>
            </Typography>
            <Box mt={6}>
              <Typography>{promotedLesson?.attributes.description}</Typography>
            </Box>
          </Box>
          <Box mt={4} width="100%">
            <PromotedLessons
              bookingServiceId={promotedLesson?.attributes.bookingServiceId}
              activityId={activityId}
            />
          </Box>
        </Flex>
      ))}
      <Flex
        alignSelf="flex-end"
        position="relative"
        id="lectors"
        ml={{ xs: 0, sm: 15 }}
        flexDirection={{ xs: "column", sm: "row" }}
        height="100%"
        mt={{ xs: 5, sm: 0 }}
      >
        {orderedLectors?.map((l, index) => (
          <AnimatingBlock
            width={180}
            percentageHeight={80}
            mobileHeight={140}
            disableHeightAnimation
            noBackground
            key={l.id}
            disableAnimationOnMobile
            index={index}
            render={({ opacityContentStyle }) => (
              <SameColorContainer>
                <AbsoluteRotatedFlex p={3} zIndex={1}>
                  <Box sx={{ display: { xs: "none", sm: "block" } }}>
                    <animated.div style={opacityContentStyle}>
                      <Link
                        sx={{ mt: 3 }}
                        href={`${routes.lectors}/${l.attributes.slug}`}
                        endIcon={<DoubleArrow />}
                      >
                        O mně
                      </Link>
                    </animated.div>
                  </Box>
                  <Link href={`${routes.lectors}/${l.attributes.slug}`}>
                    <Typography
                      variant="h4"
                      fontSize={{ xs: "3rem", md: "4rem" }}
                    >
                      {l?.attributes?.name.split(" ")?.[0]}
                    </Typography>
                  </Link>
                </AbsoluteRotatedFlex>
                <LectorImage
                  isodd={index % 2 === 0 ? "true" : undefined}
                  imageoffset={l?.attributes.imageOffset}
                  width="100%"
                >
                  {l?.attributes?.image?.data?.attributes?.url && (
                    <Image
                      src={getImageUrl(
                        l?.attributes?.image?.data?.attributes?.url
                      )}
                      layout="fill"
                      objectFit="cover"
                      alt={l.attributes?.name}
                    />
                  )}
                </LectorImage>
              </SameColorContainer>
            )}
          />
        ))}
      </Flex>
    </>
  );
};

export default TryItContent;

const SameColorContainer = styled(Flex)`
  position: relative;

  h4 {
    color: ${({ theme }) => theme.palette.text.primary};
  }
`;

const AbsoluteRotatedFlex = styled(Flex)`
  position: absolute;
  left: 50%;
  bottom: 25%;
  transform: translate(-50%, -50%) rotate(-90deg);
  white-space: nowrap;

  div:nth-of-type(1) {
    a {
      transform: rotate(90deg);
      width: 3rem;
      transform-origin: left top;
    }
  }

  ${(props) => props.theme.breakpoints.down("sm")} {
    transform: none;
    left: 0;
    top: 0;
    bottom: auto;
    flex-direction: column-reverse;

    a {
      transform: none;
    }
  }
`;

const LectorImage = styled(Box)<{ isodd?: string; imageoffset?: number }>`
  height: ${({ isodd }) => (isodd ? "110%" : "100%")};
  width: 100%;
  position: absolute;
  filter: saturate(0);
  z-index: 0;

  ${(props) => props.theme.breakpoints.down("md")} {
    width: 100%;
  }

  span {
    ${(props) => props.theme.breakpoints.down("md")} {
      width: 100%;
      ${({ imageoffset }) =>
        (imageoffset || 0) >= 0
          ? `
        margin-top: ${-(imageoffset || 0)}px !important`
          : `margin-bottom: ${imageoffset || 0}px !important`}
    }
  }
`;

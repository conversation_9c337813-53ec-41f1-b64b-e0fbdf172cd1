import { DoubleArrow } from "@mui/icons-material";
import {
  Button,
  IconButton,
  styled,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
} from "@mui/material";
import { getFormattedDate, getFormattedTime } from "../../helpers/time";
import { useDialog } from "../../providers/DialogProvider";
import { Class } from "../../reservanto/types";
import { getTimeFromUnixTimeStamp } from "../booking/helper";
import { LoadingPlaceholderIndicator } from "../LoadingPlaceholderIndicator";

interface Props {
  data?: Class[];
  isLoading: boolean;
}

const Calendar = ({ data, isLoading }: Props) => {
  const { goTo } = useDialog();

  const handleSetDialog = (appointmentId?: number | null) => {
    goTo("booking", {
      appointmentId,
    });
  };

  const renderLoading = (width: number) => (
    <LoadingPlaceholderIndicator sx={{ width, height: 26 }} />
  );

  const renderRow = (data?: Class, id?: string) => {
    const date =
      data?.StartDate &&
      getFormattedDate(getTimeFromUnixTimeStamp(data.StartDate));
    const time =
      data?.StartDate &&
      getFormattedTime(getTimeFromUnixTimeStamp(data.StartDate));
    const location = data?.CalendarName;
    return (
      <TableRow
        key={id || data?.Id}
        sx={{
          td: {
            borderRight: 0,
          },
        }}
      >
        <TableCell
          sx={{
            fontSize: { xs: "0.75rem", md: "1.25rem" },
            fontWeight: 900,
            whiteSpace: "nowrap",
          }}
        >
          {date || renderLoading(30)}
        </TableCell>
        <TableCell
          sx={{
            fontSize: {
              xs: "0.75rem",
              md: "1.25rem",
              whiteSpace: "nowrap",
            },
          }}
        >
          {time || renderLoading(30)}
        </TableCell>
        <TableCell sx={{ whiteSpace: "nowrap" }}>
          {location || renderLoading(120)}
        </TableCell>
        <TableCell sx={{ whiteSpace: "nowrap", paddingRight: 0 }}>
          {!isLoading && (
            <Button
              sx={{
                fontWeight: 900,
                fontSize: "1.1rem",
                display: { xs: "none", md: "inline-flex" },
              }}
              endIcon={<DoubleArrow />}
              onClick={() => handleSetDialog(data?.Id)}
            >
              Přihlásit se
            </Button>
          )}
          <IconButton
            sx={{
              fontWeight: 900,
              fontSize: "1.1rem",
              color: "primary.main",
              display: { xs: "block", md: "none" },
            }}
            onClick={() => handleSetDialog(data?.Id)}
          >
            <DoubleArrow />
          </IconButton>
        </TableCell>
      </TableRow>
    );
  };

  const renderLoadingPlaceholder = () => {
    return (
      <>
        {renderRow(undefined, "placeholder-1")}
        {renderRow(undefined, "placeholder-2")}
      </>
    );
  };

  return (
    <Table>
      <TableHead>
        <TableRow>
          <HeaderCell>DEN</HeaderCell>
          <HeaderCell>ČAS</HeaderCell>
          <HeaderCell>MÍSTO</HeaderCell>
          <HeaderCell />
        </TableRow>
      </TableHead>
      <TableBody>
        {isLoading
          ? renderLoadingPlaceholder()
          : data?.slice(0, 3)?.map((c) => renderRow(c))}
      </TableBody>
    </Table>
  );
};

export default Calendar;

const HeaderCell = styled(TableCell)`
  font-size: 0.55rem;
`;

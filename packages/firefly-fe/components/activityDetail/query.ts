import { QueryFunctionContext } from "@tanstack/react-query";
import { axiosApi, axiosCms, StrapiArrayResponse } from "../../axios";
import { Class } from "../../reservanto/types";
import { Lesson } from "../../strapi/types";
import { getTimeInUnixTimeStamp } from "../booking/helper";
import { ActivityResponse } from "./types";

////////////// CMS //////////////

export const getActivity = async ({
  queryKey,
}: QueryFunctionContext<[string, { slug: string }]>): Promise<
  StrapiArrayResponse<ActivityResponse>
> => {
  const [, { slug }] = queryKey;
  const response = await axiosCms.get(`/activities`, {
    params: {
      "filters[slug]": slug,
      "populate[0]": "lectors.image",
      "populate[1]": "image",
      "populate[2]": "lesson_types",
    },
  });
  return response.data;
};

export const getPromotedLesson = async ({
  queryKey,
}: QueryFunctionContext<[string, { activityId: string | number }]>): Promise<StrapiArrayResponse<Lesson>> => {
  const [, { activityId }] = queryKey;
  const response = await axiosCms.get(`/lessons`, {
    params: {
      "filters[activity][id][$eq]": activityId,
      "filters[isPromoted][$eq]": true,
    },
  });
  return response.data;
};

////////////// API //////////////

export const getClassesForLesson = async ({
  queryKey,
}: QueryFunctionContext<
  [string, { bookingServiceId: number }]
>): Promise<Class[]> => {
  const [, { bookingServiceId }] = queryKey;
  const from = getTimeInUnixTimeStamp(new Date());
  // + two months
  const to = from + 18596000;
  const response = await axiosApi.get(`/classes/forLesson`, {
    params: {
      bookingServiceId,
      from,
      to
    },
  });
  return response.data;
};

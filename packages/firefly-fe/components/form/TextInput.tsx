import { TextField, TextFieldProps } from "@mui/material";

type Props = {
  inputRegex?: RegExp;
} & TextFieldProps;

const allowedEventKeys = ["Backspace", "Shift", "ArrowLeft", "Delete", "ArrowRight"];

export const TextInput = ({ inputRegex, helperText, ...restProps }: Props) => {
  const handleKeyPress = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (inputRegex) {
      if (
        !inputRegex.test(event.key) &&
        !allowedEventKeys.includes(event.key) &&
        !event.metaKey
      ) {
        event.preventDefault();
      }
    }
  };

  return (
    <TextField
      {...restProps}
      onKeyDown={handleKeyPress}
      error={!!helperText}
      helperText={helperText}
    />
  );
};

import { AxiosResponse } from "axios";
import { axiosApi } from "../../axios";
import { Customer } from "../../reservanto/types";

export interface LoginInput {
  email: string;
  password: string;
}
export interface LoginResponse {
  accessToken: string;
  refreshToken: string;
}
export const loginUser = async (input: LoginInput): Promise<LoginResponse> => {
  const response = await axiosApi({
    method: "POST",
    url: "/auth/login",
    data: input,
  });
  return response.data;
};

export interface RegisterInput {
  name: string;
  phone: string;
  // this should be required tho
  password?: string;
  email: string;
}
export const registerUser = async (input: RegisterInput): Promise<void> => {
  await axiosApi({
    method: "POST",
    url: "/auth/register",
    data: input,
  });
  return;
};

export interface UpdateInput {
  name: string;
  phone: string;
  password?: string;
  email: string;
}
export const updateMe = async (input: UpdateInput): Promise<Customer> => {
  const response = await axios<PERSON><PERSON>({
    method: "PUT",
    url: "/me",
    data: input,
  });
  return response.data;
};

export interface ResetPasswordInput {
  email: string;
}
export const resetUserPassword = async (
  input: ResetPasswordInput
): Promise<void> => {
  await axiosApi({
    method: "POST",
    url: "/auth/reset-password",
    data: input,
  });
  return;
};

export const getMe = async (
  _: any,
  cookies?: {
    [key: string]: string;
  }
): Promise<Customer> => {
  let response: AxiosResponse<any, any> | null = null;
  if (cookies) {
    const cookiesString = Object.keys(cookies)
      .map((key) => `${key}=${cookies[key]}`)
      .join("; ");

    response = await axiosApi("/me", {
      headers: {
        Cookie: cookiesString,
      },
    });
  } else {
    response = await axiosApi("/me");
  }
  return response.data;
};

export const refreshAccessToken = async (
  refreshToken: string
): Promise<{
  accessToken: string;
}> => {
  const response = await axiosApi({
    method: "POST",
    url: "/auth/refresh-token",
    data: {
      refreshToken,
    },
  });
  return response.data;
};

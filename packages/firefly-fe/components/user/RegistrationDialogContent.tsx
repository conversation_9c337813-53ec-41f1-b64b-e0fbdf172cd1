import { useDialog } from "../../providers/DialogProvider";
import { useMe } from "../../providers/MeProvider";
import {
  ProfileInfoForm,
  ProfileInfoFormValues,
} from "../profile/ProfileInfoForm";
import { Button } from "@mui/material";

export const RegistrationDialogContent = () => {
  const { register, meIsLoading } = useMe();
  const { goBack } = useDialog();

  const handleRegistration = async (values: ProfileInfoFormValues) => {
    const correctValues = {
      ...values,
    };
    delete correctValues.passwordAgain;
    try {
      register?.mutate(correctValues, {
        onSuccess: () => {
          goBack();
        }
      });
    } catch (e) {
      console.log(e);
    }
  };

  return (
      <ProfileInfoForm
        onSubmit={handleRegistration}
        renderSubmitButton={() => (
          <Button type="submit" color="secondary" variant="contained" disabled={register?.isLoading || meIsLoading}>
            Registrovat se
          </Button>
        )}
      />
  );
};

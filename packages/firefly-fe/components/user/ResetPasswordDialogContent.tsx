import { Typography } from "@mui/material";
import { useDialog } from "../../providers/DialogProvider";
import { useMe } from "../../providers/MeProvider";
import {
  ResetPasswordForm,
  ResetPasswordFormValues,
} from "./ResetPasswordForm";

export const ResetPasswordDialogContent = () => {
  const { resetPassword } = useMe()
  const { goBack } = useDialog()

  const handleResetPassword = (values: ResetPasswordFormValues) => {
    resetPassword?.mutate(values, {
      onSuccess: () => {
        goBack();
      }
    })
  };

  return (
    <>
      <Typography
        sx={{ fontSize: "1rem", lineHeight: 1.75, color: "common.white" }}
      >
        Zadejte e-mail, pod kterým jsi registrovaný/á
      </Typography>
      <ResetPasswordForm onSubmit={handleResetPassword} />
    </>
  );
};

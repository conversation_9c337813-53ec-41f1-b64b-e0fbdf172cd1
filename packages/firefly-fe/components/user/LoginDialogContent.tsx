import { DoubleArrow } from "@mui/icons-material";
import { Button, styled, Typography } from "@mui/material";
import { useDialog } from "../../providers/DialogProvider";
import { useMe } from "../../providers/MeProvider";
import { LoginForm, LoginFormValues } from "./LoginForm";

export const LoginDialogContent = () => {
  const { goTo, close, dialog, goBack } = useDialog();
  const { login } = useMe();

  const handleLogin = async (values: LoginFormValues) => {
    login?.mutate(values, {
      onSuccess: () => {
        if (dialog.appointmentId) {
          goBack();
          goTo("bookingInfo");
          return;
        }
        close();
      },
    });
  };

  return (
    <>
      <Typography
        sx={{ fontSize: "1rem", lineHeight: 1.75, color: "common.white" }}
      >
        N<PERSON><PERSON><PERSON> uživatelský účet?&nbsp;&nbsp;
        <RegistrationButton
          variant="text"
          color="secondary"
          sx={{ display: "context", mt: -0.5 }}
          onClick={() => goTo("registration")}
          endIcon={<DoubleArrow />}
        >
          Zaregistruj se
        </RegistrationButton>
      </Typography>
      <LoginForm onSubmit={handleLogin} isSubmiting={!!login?.isLoading} />
      <RegistrationButton
        variant="text"
        color="secondary"
        onClick={() => goTo("resetPassword")}
        sx={{
          marginTop: 2,
        }}
        endIcon={<DoubleArrow />}
      >
        Nepamatuješ si heslo?
      </RegistrationButton>
    </>
  );
};

const RegistrationButton = styled(Button)`
  font-weight: normal;
  padding: 0;
  letter-spacing: normal;
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen,
    Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;
`;

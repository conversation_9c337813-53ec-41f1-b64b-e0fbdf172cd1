import { yup<PERSON><PERSON>olver } from "@hookform/resolvers/yup";
import { Button, TextField } from "@mui/material";
import { Controller, useForm } from "react-hook-form";
import { Flex } from "../commonStyledComponents";
import * as yup from "yup";

const schema = yup
  .object()
  .shape({
    email: yup.string().required("Toto pole je požadované"),
  })
  .required("Toto pole je požadované");

export interface ResetPasswordFormValues {
  email: string;
}

interface Props {
  onSubmit: (values: ResetPasswordFormValues) => void;
}

export const ResetPasswordForm = ({ onSubmit }: Props) => {
  const {
    handleSubmit,
    control,
    formState: { errors },
  } = useForm<ResetPasswordFormValues>({
    resolver: yupResolver(schema),
  });

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Flex flexDirection="column" mt={4}>
        <Controller
          control={control}
          name="email"
          render={({ field }) => (
            <TextField
              {...field}
              variant="outlined"
              placeholder="E-mail"
              sx={{ marginBottom: 3 }}
              helperText={errors.email?.message}
              error={!!errors.email?.message}
            />
          )}
        />
        <Button type="submit" color="secondary" variant="contained">
          Zaslat nové heslo
        </Button>
      </Flex>
    </form>
  );
};

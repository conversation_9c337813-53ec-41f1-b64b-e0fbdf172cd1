import { Button } from "@mui/material";
import { Controller, useForm } from "react-hook-form";
import { Flex } from "../commonStyledComponents";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { TextInput } from "../form/TextInput";

const schema = yup
  .object()
  .shape({
    email: yup.string().required("Toto pole je požadované"),
    password: yup.string().required("Toto pole je požadované"),
  })
  .required("Toto pole je požadované");

interface Props {
  onSubmit: (values: LoginFormValues) => void;
  isSubmiting: boolean;
}

export interface LoginFormValues {
  email: string;
  password: string;
}

export const LoginForm = ({ onSubmit, isSubmiting }: Props) => {
  const {
    handleSubmit,
    control,
    formState: { errors },
  } = useForm<LoginFormValues>({
    resolver: yupResolver(schema),
  });

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Flex flexDirection="column" mt={4}>
        <Controller
          control={control}
          name="email"
          render={({ field }) => (
            <TextInput
              {...field}
              variant="outlined"
              placeholder="E-mail"
              type="email"
              sx={{ marginBottom: 2 }}
              helperText={errors.email?.message}
              error={!!errors.email?.message}
            />
          )}
        />
        <Controller
          control={control}
          name="password"
          render={({ field }) => (
            <TextInput
              {...field}
              variant="outlined"
              placeholder="Heslo"
              type="password"
              sx={{ marginBottom: 3 }}
              helperText={errors.password?.message}
              error={!!errors.password?.message}
            />
          )}
        />
        <Button
          type="submit"
          color="secondary"
          variant="contained"
          disabled={isSubmiting}
        >
          Přihlásit se
        </Button>
      </Flex>
    </form>
  );
};

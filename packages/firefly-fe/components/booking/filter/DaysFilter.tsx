import styled from "@emotion/styled";
import { Box } from "@mui/material";
import { Class } from "../../../reservanto/types";
import Link from "../../Link";
import { days } from "../data";
import { getWeekDays } from "../helper";

const daysOfWeek = getWeekDays(new Date());

interface Props {
  classes?: Class[];
}

export const DaysFilter = ({ classes }: Props) => {
  return (
    <StyledContainer sx={{ display: { sx: "block", md: "none" } }}>
      {daysOfWeek.map((day, index) => {
        const currentDayNumber = day.getDay();
        const classesForDay = classes?.filter((c) => {
          const dayOfWeek = new Date(c.StartDate * 1000).getDay();
          return currentDayNumber === dayOfWeek;
        });

        const disabled = (classesForDay?.length || 0) < 1;

        return (
          <Link
            href={`#day-${currentDayNumber}`}
            disabled={disabled}
            key={`${day.toISOString()}-${index}`}
            sx={{ pb: 4, color: disabled ? "divider" : "common.black" }}
          >
            {days[index % 7].name.slice(0, 2).toUpperCase()}
          </Link>
        );
      })}
    </StyledContainer>
  );
};

const StyledContainer = styled(Box)`
  position: absolute;
  right: 0;
  padding-right: 1rem;
`;

import { Circle } from "@mui/icons-material";
import {
  Checkbox,
  FormControl,
  FormControlLabel,
  FormGroup,
  css,
  styled,
} from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import { getActivities } from "../query";
import { FilterStateQuery } from "./Filters";

interface Props {
  onChange: (checked: boolean, id: string, name: string) => void;
  filters: FilterStateQuery;
  isMobile?: boolean;
}

export const ActivityFilter = ({ onChange, filters, isMobile }: Props) => {
  const { data } = useQuery(["activities"], getActivities);

  const isChecked = (activityId: number) =>
    filters["activityIds"]?.includes(activityId.toString());

  return (
    <FormControl>
      <StyledFormGroup row aria-labelledby="activity" sx={{ flexWrap: "wrap" }}>
        {data?.data
          .sort((a, b) => a.id - b.id)
          ?.map((activity) => (
            <StyledFormControlLabel
              key={activity.id}
              ismobile={isMobile}
              control={
                <Checkbox
                  icon={<Circle fontSize="medium" />}
                  checkedIcon={<Circle fontSize="medium" />}
                  checked={!!isChecked(activity.id)}
                  onChange={(_, checked) =>
                    onChange(checked, activity.id.toString(), "activityIds")
                  }
                />
              }
              label={activity.attributes.title}
            />
          ))}
      </StyledFormGroup>
    </FormControl>
  );
};

const StyledFormControlLabel = styled(FormControlLabel)<{ ismobile?: boolean }>`
  span {
    font-weight: 700;
    ${({ ismobile }) =>
      !ismobile
        ? css`
            font-family: "HrotBasic";
          `
        : css`
            color: white;
          `}
    font-size: 1rem;
    white-space: nowrap;
  }
`;

const StyledFormGroup = styled(FormGroup)`
  gap: 1rem;
  width: 100%;

  ${({ theme }) => ({
    [theme.breakpoints.down("sm")]: {
      padding: "0.25rem",
      overflowX: "auto",
    },
  })}
`;

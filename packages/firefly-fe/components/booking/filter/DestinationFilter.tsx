import { useRouter } from "next/router";
import { groupBy, sortBy } from "rambda";
import { SyntheticEvent, useEffect } from "react";
import { Switch } from "../../switch/Switch";

interface Props {
  onChange: (
    checked: boolean | null,
    id: string,
    name: string,
    type: "OR" | "ADD"
  ) => void;
}

const calendars = [
  {
    value: "22246-22478",
    subLabel: "I.P. Pavlova",
    label: "SÁL 1",
  },
  {
    value: "22293-22490",
    subLabel: "I.P. Pavlova",
    label: "SÁL 2",
  },
   {
    value: "30243-22490",
    subLabel: "I.P. Pavlova",
    label: "SÁL 3",
  },
  {
    value: "22294-22491",
    subLabel: "Studio",
    label: "Hradčanská",
  },
];

export const DestinationFilter = ({ onChange }: Props) => {
  const { query } = useRouter();

  useEffect(() => {
    const sortedCalendars = sortBy((c) => c.subLabel, calendars);
    if (query.destinationId == null && calendars.length > 0) {
      const groupedCalendarsByName = groupBy((c) => c.label, sortedCalendars);

      onChange(
        null,
        groupedCalendarsByName[sortedCalendars[0].label]
          .map((c) => c.value)
          .join("-"),
        "destinationId",
        "OR"
      );
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [calendars]);

  const handleChange = (
    _: SyntheticEvent<Element, Event>,
    newIndex: number
  ) => {
    const foundLocation = calendars[newIndex];
    const groupedCalendarsByName = groupBy((c) => c.label, calendars);
    const selectedCalendar = groupedCalendarsByName[foundLocation.label];
    onChange(
      null,
      selectedCalendar.map((c) => c.value).join("-"),
      "destinationId",
      "OR"
    );
  };

  const foundLocationIndex = calendars.findIndex((c) =>
    c.value === query.destinationId?.toString()
  );

  return (
    <Switch
      items={calendars}
      onChange={handleChange}
      activeIndex={foundLocationIndex > -1 ? foundLocationIndex : 0}
    />
  );
};

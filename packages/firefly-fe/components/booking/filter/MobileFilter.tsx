import { useQuery } from "@tanstack/react-query";
import { FilterStateQuery } from "./Filters";
import { getActivities } from "../query";
import { getLessonTypes } from "./query";
import { makeArray } from "../../../helpers/url";
import { Flex } from "../../commonStyledComponents";
import CancelIcon from "@mui/icons-material/Cancel";
import {
  Box,
  Button,
  IconButton,
  SwipeableDrawer,
  Typography,
  styled,
} from "@mui/material";
import DoubleArrow from "@mui/icons-material/DoubleArrow";
import { useState } from "react";
import { ActivityFilter } from "./ActivityFilter";
import { AlternativeFilters } from "./AlternativeFilters";

interface Props {
  filters: FilterStateQuery;
  onChange: (
    checked: boolean | null,
    id: string | number,
    name: string,
    type?: "ADD" | "OR"
  ) => void;
}

export const MobileFilter = ({ filters, onChange }: Props) => {
  const [isOpen, setIsOpen] = useState(false);
  const { data: activitiesData } = useQuery(["activities"], getActivities);
  const { data: lessonData } = useQuery(["lessonTypes"], getLessonTypes);

  const selectedFilterNames = makeArray(filters.activityIds)?.map(
    (activityId) => {
      const foundActivity = activitiesData?.data.find(
        (activity) => activity.id === Number(activityId)
      );
      return foundActivity?.attributes.title;
    }
  );

  makeArray(filters?.lessonTypeIds)?.forEach((lessonTypeId) => {
    const foundLesson = lessonData?.data.find(
      (lesson) => lesson.id === Number(lessonTypeId)
    );
    selectedFilterNames?.push(foundLesson?.attributes.title);
  });

  filters.isForBeginners && selectedFilterNames?.push("Pro začátečníky");

  const showMore = (selectedFilterNames || []).length > 2;

  return (
    <Flex mt={2} justifyContent="space-between">
      <Flex gap={1}>
        {selectedFilterNames?.slice(0, 2).map((name) => (
          <Button key={name} onClick={() => setIsOpen(true)}>
            <CancelIcon sx={{ color: "primary.main" }} />
            <Typography ml={1} fontSize="0.8rem" fontWeight={600}>
              {name}
            </Typography>
          </Button>
        ))}
      </Flex>
      {(selectedFilterNames || [])?.length > 1 && (
        <DownArrowButton onClick={() => setIsOpen(true)}>
          <DoubleArrow
            sx={{
              transform: `rotate(-90deg)`,
              color: "primary.main",
            }}
          />
          {showMore && (
            <Circle>+{(selectedFilterNames || []).length - 2}</Circle>
          )}
        </DownArrowButton>
      )}
      {(selectedFilterNames || []).length < 2 && (
        <Button
          variant="text"
          onClick={() => setIsOpen(true)}
          sx={{
            fontSize: "1rem",
            fontWeight: 900,
            fontFamily: "HrotBasic",
            alignSelf: "flex-end",
            px: { xs: 2, md: 6 },
            whiteSpace: "nowrap",
          }}
          endIcon={
            <DoubleArrow
              sx={{ transform: `rotate(${showMore ? "-90deg" : "90deg"})` }}
            />
          }
        >
          Zobrazit filtr
        </Button>
      )}

      <SwipeableDrawer
        anchor="bottom"
        open={isOpen}
        onOpen={() => setIsOpen(true)}
        onClose={() => setIsOpen(false)}
      >
        <Box px={2} py={1}>
          <IconButton
            onClick={() => setIsOpen(false)}
            sx={{ position: "absolute", right: 12, top: 16, color: "white" }}
          >
            <DoubleArrow
              sx={{
                transform: `rotate(90deg)`,
              }}
            />
          </IconButton>
          <Typography variant="h2" color="white" fontSize="2rem" mb={3}>
            Typ aktivity
          </Typography>
          <ActivityFilter
            filters={filters}
            isMobile={true}
            onChange={onChange}
          />
          <Typography variant="h3" color="white" fontSize="1.5rem" my={3}>
            Ostatní
          </Typography>
          <AlternativeFilters
            filters={filters}
            onChange={onChange}
            isOpen
            isMobile
          />
        </Box>
      </SwipeableDrawer>
    </Flex>
  );
};

const DownArrowButton = styled(Button)`
  position: relative;
`;

const Circle = styled(Flex)`
  position: absolute;
  bottom: 0;
  right: 14px;
  background-color: ${({ theme }) => theme.palette.secondary.main};
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  color: black;
  font-size: 0.575rem;
`;

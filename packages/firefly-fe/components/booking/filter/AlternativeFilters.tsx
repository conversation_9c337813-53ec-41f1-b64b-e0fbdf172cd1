import { Circle } from "@mui/icons-material";
import {
  Checkbox,
  FormControlLabel,
  FormGroup,
  css,
  styled,
} from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import { Dispatch, SetStateAction, useEffect } from "react";
import { animated, useSpring } from "react-spring";
import { FilterStateQuery } from "./Filters";
import { getLessonTypes } from "./query";

const alternativeFilters = [
  {
    value: "isForBeginners",
    label: "Pro začátečníky",
  },
];

interface Props {
  onChange: (checked: boolean, id: number | string, name: string) => void;
  isOpen: boolean;
  filters: FilterStateQuery;
  onOpen?: Dispatch<SetStateAction<boolean>>;
  isMobile?: boolean;
}

export const AlternativeFilters = ({
  isOpen,
  onChange,
  filters,
  onOpen,
  isMobile,
}: Props) => {
  const { data: lessonData } = useQuery(["lessonTypes"], getLessonTypes);
  const zoomInAnimation = useSpring({
    height: isOpen ? 60 : 0,
    opacity: isOpen ? 1 : 0,
  });

  useEffect(() => {
    const selectedLesson = lessonData?.data.find((lesson) =>
      filters["lessonTypeIds"]?.includes(lesson.id.toString())
    );
    if (selectedLesson) {
      onOpen?.(true);
      return;
    }
    const selectedAlternativeFilter = alternativeFilters.find(
      (filter) => filters[filter.value]
    );
    if (selectedAlternativeFilter) {
      onOpen?.(true);
      return;
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [lessonData?.data, onOpen]);

  const isChecked = (lessonId: number) =>
    filters["lessonTypeIds"]?.includes(lessonId.toString());

  return (
    <animated.div style={isMobile ? {} : { ...zoomInAnimation }}>
      <StyledFormGroup
        row
        aria-labelledby="activity"
        sx={{ gap: isMobile ? 2 : 4, mt: { xs: 0, md: 2 }, flexWrap: "wrap" }}
      >
        {lessonData?.data
          ?.filter((l) => l.attributes.type !== "privateLesson")
          .map((lesson) => (
            <StyledFormControlLabel
              key={lesson.id}
              ismobile={isMobile}
              control={
                <Checkbox
                  icon={<Circle fontSize="small" />}
                  checkedIcon={<Circle fontSize="small" />}
                  checked={!!isChecked(lesson.id)}
                  onChange={(_, checked) =>
                    onChange(checked, lesson.id, "lessonTypeIds")
                  }
                />
              }
              label={lesson.attributes.title}
            />
          ))}
        {alternativeFilters
          ?.map((filter) => (
            <StyledFormControlLabel
              key={filter.value}
              ismobile={isMobile}
              control={
                <Checkbox
                  icon={<Circle fontSize="small" />}
                  checkedIcon={<Circle fontSize="small" />}
                  onChange={(_, checked) =>
                    onChange(checked, "true", filter.value)
                  }
                  checked={!!filters[filter.value]}
                />
              }
              label={filter.label}
            />
          ))
          .reverse()}
      </StyledFormGroup>
    </animated.div>
  );
};

const StyledFormControlLabel = styled(FormControlLabel)<{ ismobile?: boolean }>`
  span {
    white-space: nowrap;

    ${({ ismobile }) =>
      ismobile
        ? css`
            color: white;
          `
        : ""}
  }
`;

const StyledFormGroup = styled(FormGroup)`
  flex-wrap: nowrap;
  gap: 1rem;

  ${({ theme }) => ({
    [theme.breakpoints.down("sm")]: {
      padding: "0.25rem",
      overflow: "auto",
    },
  })}
`;

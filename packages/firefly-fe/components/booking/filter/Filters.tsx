import { DoubleArrow } from "@mui/icons-material";
import { Box, Button, styled } from "@mui/material";
import { useRouter } from "next/router";
import { useEffect, useState } from "react";
import { makeArray } from "../../../helpers/url";
import { routes } from "../../../routes";
import { Flex } from "../../commonStyledComponents";
import { ActivityFilter } from "./ActivityFilter";
import { AlternativeFilters } from "./AlternativeFilters";
import { DestinationFilter } from "./DestinationFilter";
import { MobileFilter } from "./MobileFilter";

export interface FilterStateQuery {
  destinationId: null | string;
  activityIds: null | string[];
  lessonTypeIds: null | string[];
  isForBeginners: string;
}

export interface Calendar {
  CalendarId: number;
  CalendarName: string;
}

export const Filters = () => {
  const [stuck, setStuck] = useState(false)
  const [showMore, setShowMore] = useState(false);
  const { push, query } = useRouter();
  const filters: FilterStateQuery = query as any;

  useEffect(() => {
    const onScroll = () => {
      setStuck(window.scrollY > 130)
    }
    // clean up code
    window.removeEventListener('scroll', onScroll);
    window.addEventListener('scroll', onScroll, { passive: true });
    return () => window.removeEventListener('scroll', onScroll);
  }, []);

  const handleChange = (
    checked: boolean | null,
    id: string | number,
    name: string,
    type: "ADD" | "OR" = "ADD"
  ) => {
    if (type === "ADD") {
      const filterValues = makeArray(query[name]);
      if (checked) {
        push(
          {
            pathname: routes.booking,
            query: {
              ...query,
              [name]: [...(filterValues || []), id.toString()],
            },
          },
          undefined,
          {
            shallow: true,
          }
        );
        return;
      }

      const filteredActivityFilterValues = filterValues?.filter(
        (value) => value !== id.toString()
      );
      push(
        {
          pathname: routes.booking,
          query: {
            ...query,
            [name]: filteredActivityFilterValues,
          },
        },
        undefined,
        {
          shallow: true,
        }
      );
      return;
    }

    push(
      {
        pathname: routes.booking,
        query: {
          ...query,
          [name]: id,
        },
      },
      undefined,
      {
        shallow: true,
      }
    );
  };

  return (
    <FilterWrapper stuck={stuck}>
      <DestinationFilter onChange={handleChange} />
      <Box display={{ xs: "none", md: "block" }}>
        <Flex alignItems="center" flexDirection="row" mt={2}>
          <ActivityFilter onChange={handleChange} filters={filters} />
          <Button
            variant="text"
            onClick={() => setShowMore((prevState) => !prevState)}
            sx={{
              fontSize: "1rem",
              fontWeight: 900,
              fontFamily: "HrotBasic",
              alignSelf: "flex-end",
              px: { xs: 2, md: 6 },
              whiteSpace: "nowrap",
            }}
            endIcon={
              <DoubleArrow
                sx={{ transform: `rotate(${showMore ? "-90deg" : "90deg"})` }}
              />
            }
          >
            {showMore ? "Méně filtrů" : "Více filtrů"}
          </Button>
        </Flex>
        <AlternativeFilters
          isOpen={showMore}
          onChange={handleChange}
          onOpen={setShowMore}
          filters={filters}
        />
      </Box>
      <Box display={{ xs: "block", md: "none" }}>
        <MobileFilter filters={filters} onChange={handleChange} />
      </Box>
    </FilterWrapper>
  );
};

const FilterWrapper = styled(Box) <{ stuck: boolean }>`
  ${({ theme, stuck }) => ({
    [theme.breakpoints.down("sm")]: {
      position: 'sticky',
      top: '60px',
      marginInline: -8,
      paddingInline: 8,
      paddingTop: 16,
      paddingBottom: 4,
      zIndex: 4,
      background: theme.palette.background.default,
      boxShadow: stuck ? "0px 4px 5px rgb(149 149 149 / 50%)" : 'none'
    },
  })}
`;

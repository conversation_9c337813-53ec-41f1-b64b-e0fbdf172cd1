import { QueryFunctionContext } from "@tanstack/react-query";
import { CourseDetail } from "../../api/types";
import { axiosApi, axiosCms, StrapiArrayResponse } from "../../axios";
import { Class } from "../../reservanto/types";
import { Activity } from "../../strapi/types";

export const getActivities = async (): Promise<
  StrapiArrayResponse<Activity>
> => {
  const response = await axiosCms.get(`/activities`);
  return response.data;
};

export interface GetClassesParams {
  from: number;
  to: number;
  isForBeginners?: string;
  lessonTypeIds?: string[];
  activityIds?: string[];
  destinationId?: string;
}
export const getClasses = async ({
  queryKey,
}: QueryFunctionContext<[string, GetClassesParams]>): Promise<Class[]> => {
  const [
    _,
    { from, to, destinationId, activityIds, isForBeginners, lessonTypeIds },
  ] = queryKey;
  const response = await axiosApi({
    method: "get",
    url: "/classes",
    params: {
      destinationId,
      activityIds,
      lessonTypeIds,
      from,
      to,
      isForBeginners,
    },
  });
  return response.data;
};

export interface CreateBookingInput {
  customerNote?: string;
  courseId: number;
  appointmentId: number;
  customerId: number;
  bookingServiceId: number;
  isAlternate?: boolean;
}
export const createBooking = async (
  input: CreateBookingInput
): Promise<CreateBookingInput> => {
  const { customerNote, appointmentId, customerId, courseId, isAlternate } =
    input;
  if (isAlternate) {
    await axiosApi({
      method: "post",
      url: "/booking/alternate",
      data: {
        appointmentId,
        courseId,
        customerId,
      },
    });
    return input;
  }

  await axiosApi({
    method: "post",
    url: "/booking",
    data: {
      customerNote,
      appointmentId,
      courseId,
      customerId,
    },
  });
  return input;
};

export interface CancelBookingInput {
  appointmentId?: number;
  enableCancelWholeCourse?: boolean;
  isAlternate?: boolean;
  courseId?: number | null;
}
export const cancelBooking = async ({
  appointmentId,
  enableCancelWholeCourse,
  isAlternate,
  courseId,
}: CancelBookingInput): Promise<void> => {
  if (isAlternate) {
    await axiosApi({
      method: "delete",
      url: `/booking/alternate`,
      params: {
        appointmentId,
        courseId,
      },
    });
    return;
  }

  appointmentId &&
    (await axiosApi({
      method: "delete",
      url: `/booking/${appointmentId}`,
      params: {
        enableCancelWholeCourse,
      },
    }));
  return;
};

interface GetCourseInput {
  appointmentId: number | null;
}
export const getCourseDetail = async ({
  queryKey,
}: QueryFunctionContext<[string, GetCourseInput]>): Promise<CourseDetail> => {
  const [_, { appointmentId }] = queryKey;
  const response = await axiosApi({
    method: "get",
    url: `/courses/${appointmentId}`,
  });
  return response.data;
};

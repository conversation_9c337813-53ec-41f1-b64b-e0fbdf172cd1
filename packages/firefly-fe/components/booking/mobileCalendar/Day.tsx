import { Box, Divider, Typography } from "@mui/material";
import { useRouter } from "next/router";
import { groupBy, uniqBy } from "rambda";
import { padTo2Digits } from "../../../helpers/time";
import { AlternateEvent, Class } from "../../../reservanto/types";
import { Flex } from "../../commonStyledComponents";
import { days } from "../data";
import { RoundedClassButton } from "../styledComponents";
import { Event } from "../../../reservanto/types";
import { LoadingPlaceholderIndicator } from "../../LoadingPlaceholderIndicator";
import moment from "moment-timezone";

interface Props {
  classes?: Class[];
  day: Date;
  setActiveClass: (c: Class) => void;
  reservations: (Event | AlternateEvent)[] | undefined;
  isLoading: boolean;
}

export const Day = ({
  day,
  classes,
  setActiveClass,
  reservations,
  isLoading,
}: Props) => {
  const { query } = useRouter();
  const { appointmentId } = query;
  const currentDayNumber = day.getDay();

  const classesForDay = classes?.filter((c) => {
    const dayOfWeek = new Date(c.StartDate * 1000).getDay();
    return currentDayNumber === dayOfWeek;
  });

  if ((classesForDay?.length || 0) < 1 && !isLoading) {
    return null;
  }

  const uniqueClasses = uniqBy((c) => {
    const startDate = new Date(c.StartDate * 1000);
    const day = startDate.getDay();
    const hour = startDate.getHours();
    const minutes = startDate.getMinutes();
    return `${c.BookingServiceId}-${day}-${hour}-${minutes}-${c.CourseId || "-"
      }`;
  }, classesForDay || []).sort((a, b) => a.StartDate - b.StartDate);
  const groupedByTime = groupBy((c) => c.StartDate.toString(), uniqueClasses);
  const sortedByTime = Object.keys(groupedByTime).sort((a, b) => {
    const timeFromA = new Date(Number(a) * 1000);
    const timeFromB = new Date(Number(b) * 1000);

    return timeFromA.getHours() - timeFromB.getHours();
  });

  const renderLoadingPlaceholder = () => (
    <Box my={2}>
      <LoadingPlaceholderIndicator
        sx={{ width: "100%", height: 50, borderRadius: 2, mb: 2 }}
      />
      <LoadingPlaceholderIndicator
        sx={{ width: "100%", height: 50, borderRadius: 2 }}
      />
    </Box>
  );

  const dayNumber = currentDayNumber - 1;

  return (
    <Box className="fake-anchor" id={`day-${currentDayNumber}`}>
      <Typography fontWeight={900} fontSize="1rem">
        {days[dayNumber < 0 ? 6 : dayNumber]?.name}
      </Typography>
      <Divider sx={{ mt: 2, borderStyle: "dashed" }} />
      {isLoading
        ? renderLoadingPlaceholder()
        : sortedByTime.map((startDate, index) => {
          const classesForHour = groupedByTime[startDate];
          const firstClass = classesForHour[0];

          const timeFrom = moment.tz(
            new Date(firstClass.StartDate * 1000),
            "Europe/Prague"
          );
          const timeTo = moment.tz(
            new Date(firstClass.EndDate * 1000),
            "Europe/Prague"
          );

          return (
            <Flex key={`${firstClass.BookingServiceId}_${index}`} py={2}>
              <Typography width="30%" mt={1}>
                {timeFrom.hours()}:{padTo2Digits(timeFrom.minutes())}-
                {timeTo.hours()}:{padTo2Digits(timeTo.minutes())}
              </Typography>
              <Flex width="70%" gap={2} flexDirection="column">
                {classesForHour?.map((c) => {
                  const isAlreadyBooked = reservations?.find(
                    (r) => r.AppointmentId === c.Id
                  );
                  return (
                    <RoundedClassButton
                      key={c.Id}
                      onClick={() => setActiveClass(c)}
                      variant="contained"
                      color={
                        Number(appointmentId) === c.Id || isAlreadyBooked
                          ? "secondary"
                          : "info"
                      }
                      sx={{
                        border: `1px solid white`,
                        justifyContent: "flex-start",
                        paddingLeft: "1rem",
                        opacity: c.OccupiedCapacity >= c.Capacity ? 0.4 : 1,
                      }}
                    >
                      {c.BookingServiceName}
                    </RoundedClassButton>
                  );
                })}
              </Flex>
              {index < classesForHour.length && <Divider />}
            </Flex>
          );
        })}
    </Box>
  );
};

export default Day;

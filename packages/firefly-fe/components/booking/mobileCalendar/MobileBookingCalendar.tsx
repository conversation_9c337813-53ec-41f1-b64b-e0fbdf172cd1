import { DoubleArrow } from "@mui/icons-material";
import { Box, IconButton, styled } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import { useMe } from "../../../providers/MeProvider";
import { Class } from "../../../reservanto/types";
import { getReservations } from "../../profile/reservations/query";
import Day from "./Day";

interface Props {
  classes?: Class[];
  daysOfWeek: Date[];
  setActiveClass: (c: Class) => void;
  isLoading: boolean;
}

export const MobileBookingSchedule = ({
  daysOfWeek,
  classes,
  setActiveClass,
  isLoading,
}: Props) => {
  const { me } = useMe();
  const { data: reservations } = useQuery(["reservations"], getReservations, {
    enabled: !!me?.Id,
  });

  const [showScrollUpButton, setShowScrollUpButton] = useState(false);

  useEffect(() => {
    const onScroll = (_: Event) => {
      setShowScrollUpButton(window.pageYOffset > 400);
    };
    window.addEventListener("scroll", onScroll);
    return () => window.removeEventListener("scroll", onScroll);
  }, []);

  const handleScrollUp = () => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  return (
    <Box>
      {daysOfWeek.map((day, index) => (
        <Day
          key={day.toISOString() + index}
          reservations={reservations}
          isLoading={isLoading}
          day={day}
          classes={classes}
          setActiveClass={setActiveClass}
        />
      ))}
      {showScrollUpButton && (
        <ScrollUpButton onClick={handleScrollUp}>
          <DoubleArrow />
        </ScrollUpButton>
      )}
    </Box>
  );
};

const ScrollUpButton = styled(IconButton)`
  position: fixed;
  right: 32px;
  z-index: 2;
  bottom: 32px;
  transform: rotate(-90deg);
  background-color: ${({ theme }) => theme.palette.common.white};
  color: ${({ theme }) => theme.palette.primary.main};
  box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
`;

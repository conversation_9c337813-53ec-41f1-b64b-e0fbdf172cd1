import { Button, styled } from "@mui/material";

export const RoundedClassButton = styled(Button)`
  border-radius: 1.5rem;
  padding: 0.25rem;
  text-align: center;
  width: 100%;
  min-width: auto;
  height: 40px;
  min-width: 80px;
  font-weight: 400;
  letter-spacing: 0;
  display: flex;
  font-size: 0.75rem;
  line-height: 1.2;
  color: ${({ theme }) => theme.palette.common.black};
  font-family: inherit;
  box-shadow: none;

  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;

  :hover,
  :active {
    box-shadow: none;
    background-color: ${({ theme }) => theme.palette.secondary.main};
  }
`;

import { Box } from "@mui/material";
import { useDialog } from "../../providers/DialogProvider";
import { Class } from "../../reservanto/types";
import { DaysFilter } from "./filter/DaysFilter";
import { Filters } from "./filter/Filters";
import { getWeekDays } from "./helper";
import { MobileBookingSchedule } from "./mobileCalendar/MobileBookingCalendar";
import { ScheduleCalendar } from "./schedule/ScheduleCalendar";
import { useClasses } from "./useClasses";

export const Booking = () => {
  const daysOfWeek = getWeekDays(new Date());
  const {
    data: classesData,
    isLoading,
    isFetching,
  } = useClasses("filteredClasses");
  const { goTo } = useDialog();

  const handleSetActiveClass = (c: Class) => {
    goTo("booking", {
      appointmentId: c.Id,
    });
  };

  return (
    <>
      <Filters />
      <Box display={{ xs: "none", lgxl: "block" }} mt={3}>
        <ScheduleCalendar
          classes={classesData}
          isLoading={isFetching || isLoading}
          setActiveClass={handleSetActiveClass}
        />
      </Box>
      <Box display={{ xs: "block", lgxl: "none" }} mr={5} mt={2}>
        <DaysFilter classes={classesData} />
        <MobileBookingSchedule
          classes={classesData}
          daysOfWeek={daysOfWeek}
          setActiveClass={handleSetActiveClass}
          isLoading={isFetching || isLoading}
        />
      </Box>
    </>
  );
};

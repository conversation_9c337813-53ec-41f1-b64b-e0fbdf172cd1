import { Box } from "@mui/material";
import { useRouter } from "next/router";
import { uniqBy } from "rambda";
import { AlternateEvent, Class } from "../../../reservanto/types";
import { RoundedClassButton } from "../styledComponents";
import { getOccupiedGrids } from "./getOccupiedGrids";
import { Event } from "../../../reservanto/types";

interface Props {
  classes?: Class[];
  setActiveClass: (c: Class) => void;
  reservations: (Event | AlternateEvent)[] | undefined;
}

export const ListOfClasses = ({
  classes,
  setActiveClass,
  reservations,
}: Props) => {
  const { query } = useRouter();
  const { appointmentId } = query;
  if (classes == null) {
    return null;
  }

  const uniqueClasses = uniqBy((c) => {
    const startDate = new Date(c.StartDate * 1000);
    const day = startDate.getDay();
    const hour = startDate.getHours();
    const minutes = startDate.getMinutes();
    return `${c.BookingServiceId}-${day}-${hour}-${minutes}-${c.CourseId || "-"
      }`;
  }, classes || []);

  const gridPositions = getOccupiedGrids(uniqueClasses);

  return (
    <>
      {uniqueClasses.map((c, index) => {
        const { gridColumn, gridRow, span, position } = gridPositions[index];
        const isAlreadyBooked = reservations?.find(
          (r) => r.AppointmentId === c.Id
        );
        return (
          <Box
            key={c.Id}
            sx={{
              gridRow,
              gridColumn: `${gridColumn}/span ${span}`,
            }}
          >
            <RoundedClassButton
              onClick={() => setActiveClass(c)}
              variant="contained"
              color={
                Number(appointmentId) === c.Id || isAlreadyBooked ? "secondary" : "info"
              }
              sx={{
                border: "1px solid white",
                opacity: c.OccupiedCapacity >= c.Capacity ? 0.4 : 1,
                marginTop:
                  position === "center"
                    ? "20px"
                    : position === "bottom"
                      ? "40px"
                      : 0,
              }}
            >
              {c.BookingServiceName}
            </RoundedClassButton>
          </Box>
        );
      })}
    </>
  );
};

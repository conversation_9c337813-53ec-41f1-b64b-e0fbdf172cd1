import { Box, styled } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import { useMe } from "../../../providers/MeProvider";
import { Class } from "../../../reservanto/types";
import { Flex } from "../../commonStyledComponents";
import { getReservations } from "../../profile/reservations/query";
import { days, hours } from "../data";
import { getWorkingTimes } from "../helper";
import { Item } from "./Item";
import { ListOfClasses } from "./ListOfClasses";
import { ListOfClassesPlaceholder } from "./ListOfClassesPlaceholder";

interface Props {
  classes?: Class[];
  setActiveClass: (c: Class) => void;
  isLoading: boolean;
}

export const ScheduleCalendar = ({
  classes,
  setActiveClass,
  isLoading,
}: Props) => {
  const halfHourOfWeek = getWorkingTimes(new Date());
  const { me } = useMe();
  const { data: reservations } = useQuery(["reservations"], getReservations, {
    enabled: !!me?.Id,
  });

  return (
    <>
      <HeadGrid>
        {hours.map((hour) => (
          <Item key={hour} sx={{ border: "none" }}>
            {hour}
          </Item>
        ))}
      </HeadGrid>
      <Flex>
        <DaysGrid>
          {days.map((day, index) => (
            <Item
              key={day.name}
              sx={{
                alignItems: "center",
                display: "flex",
                borderBottom: days.length - 1 === index ? "none" : null,
              }}
            >
              {day.name}
            </Item>
          ))}
        </DaysGrid>
        <HoursGrid>
          {halfHourOfWeek.map((day, index) => (
            <Item
              key={day.toISOString()}
              sx={{
                borderRight: index % 2 === 0 ? 0 : null,
                gridColumn: (index % 30) + 1,
                gridRow: Math.floor(index / 30) + 1,
                borderBottom:
                  halfHourOfWeek.length - 30 <= index ? "none" : null,
              }}
            />
          ))}
          {isLoading && !classes?.length ? (
            <ListOfClassesPlaceholder />
          ) : (
            <ListOfClasses
              classes={classes}
              setActiveClass={setActiveClass}
              reservations={reservations}
            />
          )}
        </HoursGrid>
      </Flex>
    </>
  );
};

const HoursGrid = styled(Box)`
  display: grid;
  grid-auto-flow: row;
  grid-template-rows: repeat(7, 80px);
  grid-template-columns: repeat(30, 40px);
`;

const HeadGrid = styled(Box)`
  display: grid;
  grid-auto-flow: row;
  grid-template-rows: 50px;
  grid-template-columns: repeat(17, 80px);
`;

const DaysGrid = styled(Box)`
  display: grid;
  grid-auto-flow: row;
  grid-template-rows: repeat(7, 80px);
  grid-template-columns: 100px;
`;

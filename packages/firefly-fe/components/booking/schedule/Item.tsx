import { Box, styled, SxProps, Theme } from "@mui/material";

interface Props {
  children?: React.ReactNode;
  sx?: SxProps<Theme> | undefined;
}

export const Item = ({ children, sx }: Props) => {
  return (
    <ItemContainer position="relative" sx={sx}>
      {children}
    </ItemContainer>
  );
};

const ItemContainer = styled(Box)`
  border-bottom: ${({ theme }) => `1px dashed ${theme.palette.divider}`};
  border-right: ${({ theme }) => `1px solid ${theme.palette.divider}`};
`;

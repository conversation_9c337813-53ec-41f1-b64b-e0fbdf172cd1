import { range } from "rambda";
import { Class } from "../../../reservanto/types";
import { getTime } from "../helper";

interface OccupiedGrid {
  gridRow: number;
  gridColumn: number;
  span: number;
  position: string;
}

export const getOccupiedGrids = (classes: Class[]) => {
  let occupiedGrids: OccupiedGrid[] = [];
  classes.forEach((c) => {
    const { startDay, startHours, lessonLength, startMinutes } = getTime(
      c.StartDate,
      c.EndDate
    );

    // this condition is made because of sunday is 0 and rows starts at 1
    const gridRow = startDay === 0 ? 7 : startDay;

    let gridColumn = (startHours - 6) * 2 - 1;
    if (startMinutes === 30) {
      gridColumn += 1;
    }

    const span = Math.ceil(lessonLength / 30);
    let position = "center";

    occupiedGrids = occupiedGrids.map((grid) => {
      const prevGrid = range(grid.gridColumn, grid.span + grid.gridColumn);
      const newGrid = range(gridColumn, gridColumn + span);

      const isOccupied = prevGrid.find((g) => newGrid.includes(g));

      if (grid.gridRow === gridRow && isOccupied) {
        position = "bottom";
        return {
          ...grid,
          position: "top",
        };
      }

      return grid;
    });

    occupiedGrids.push({
      gridRow,
      gridColumn,
      span,
      position,
    });
  });

  return occupiedGrids;
};

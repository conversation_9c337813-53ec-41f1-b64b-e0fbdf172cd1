import { Box } from "@mui/material";
import { LoadingPlaceholderIndicator } from "../../LoadingPlaceholderIndicator";
import { range } from "rambda";
import React from "react";

interface OccupiedGrid {
  gridRow: number;
  gridColumn: number;
  span: number;
  position: string;
}

const randomIntFromInterval = (min: number, max: number) => {
  // min and max included
  return Math.floor(Math.random() * (max - min + 1) + min);
};

export const getRandomPositions = () => {
  let occupiedGrids: OccupiedGrid[] = [];
  const amountOfItems = randomIntFromInterval(10, 30);

  new Array(amountOfItems).fill("1").forEach((c) => {
    const gridRow = randomIntFromInterval(1, 7);
    const gridColumn = randomIntFromInterval(1, 29);

    const span = 2;
    let position = "center";

    occupiedGrids = occupiedGrids.map((grid) => {
      const prevGrid = range(grid.gridColumn, grid.span + grid.gridColumn);
      const newGrid = range(gridColumn, gridColumn + span);

      const isOccupied = prevGrid.find((g) => newGrid.includes(g));

      if (grid.gridRow === gridRow && isOccupied) {
        position = "bottom";
        return {
          ...grid,
          position: "top",
        };
      }

      return grid;
    });

    occupiedGrids.push({
      gridRow,
      gridColumn,
      span,
      position,
    });
  });
  return occupiedGrids;
};

const ListOfClassesPlaceholderBase = () => {
  const randomPositions = getRandomPositions();
  return (
    <>
      {randomPositions.map((item, index) => {
        const { gridColumn, gridRow, span, position } = item;
        return (
          <Box
            key={`placeholder-${index}`}
            sx={{
              gridRow,
              gridColumn: `${gridColumn}/span ${span}`,
            }}
          >
            <LoadingPlaceholderIndicator
              sx={{
                marginTop:
                  position === "center"
                    ? "20px"
                    : position === "bottom"
                    ? "40px"
                    : 0,
                height: 40,
                width: 80,
                borderRadius: "1.5rem",
                backgroundColor: "white",
              }}
            />
          </Box>
        );
      })}
    </>
  );
};

export const ListOfClassesPlaceholder = React.memo(ListOfClassesPlaceholderBase);

import { useQuery } from "@tanstack/react-query";
import { useRouter } from "next/router";
import { getWeekDays } from "./helper";
import { getClasses } from "./query";

const currentDate = new Date();
const days = getWeekDays(currentDate);

export const useClasses = (key: "allClasses" | "filteredClasses") => {
  const { query } = useRouter();
  // beginning of this week
  const firstDay = days[0];
  firstDay.setHours(0);
  const from = Math.ceil(firstDay.getTime() / 1000);

  // + two months
  const to = from + 18596000;

  let queryParams: any = {
    from,
    to,
  };

  if (key === "filteredClasses") {
    queryParams = {
      ...queryParams,
      isForBeginners: query.isForBeginners as string,
      lessonTypeIds: query.lessonTypeIds as string,
      activityIds: query.activityIds as string[],
      destinationId:
        key === "filteredClasses" ? (query.destinationId as string) : undefined,
    };
  }

  return useQuery([key, queryParams], getClasses, {
    enabled: key === "allClasses" || query.destinationId != null
  });
};

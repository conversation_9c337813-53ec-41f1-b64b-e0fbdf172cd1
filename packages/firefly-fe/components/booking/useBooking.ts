import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useDialog } from "../../providers/DialogProvider";
import { useMe } from "../../providers/MeProvider";
import { useSnackbar } from "../../providers/SnackbarProvider";
import { availablePastoPay } from "./payment/helpers";
import { useMakePayment } from "./payment/useMakePayment";
import * as queries from "./query";
import { useRouter } from "next/router";
import { routes } from "../../routes";

export const useBooking = () => {
  const client = useQueryClient();
  const { me } = useMe();
  const { goTo } = useDialog();
  const { showSnackbar } = useSnackbar();
  const makePayment = useMakePayment();
  const { push, query } = useRouter();

  const addSentStatusParam = () => {
    push(
      {
        pathname: routes.booking,
        query: {
          ...query,
          status: "sent",
        },
      },
      undefined,
      {
        shallow: true,
      }
    );
  };

  const createBooking = useMutation(queries.createBooking, {
    onSuccess: (input) => {
      const passToApply = availablePastoPay(input.bookingServiceId, me!);
      if (passToApply && !input.isAlternate) {
        makePayment.mutate(
          {
            type: "seasonPass",
            appointmentId: input.appointmentId,
            passUsageId: passToApply,
          },
          {
            onSuccess: () => {
              addSentStatusParam();
              goTo("bookingDone");
            },
          }
        );
      } else {
        addSentStatusParam();
        goTo("bookingDone");
      }
      showSnackbar({
        severity: "success",
        message: "Rezervace byla vytvořena",
      });
    },
    onError: (data: any) => {
      showSnackbar({
        severity: "error",
        message: data.response.data,
      });
    },
  });

  const cancelBooking = useMutation(queries.cancelBooking, {
    onSuccess: () => {
      client.invalidateQueries(["reservations"]);
      client.invalidateQueries(["replacements"]);
      client.invalidateQueries(["me"]);
      client.invalidateQueries(["courseCustomerEvents"]);
      client.invalidateQueries(["creditTransactions"]);
      showSnackbar({
        severity: "success",
        message: "Rezervace byla zrušena",
      });
    },
    onError: (data: any) => {
      showSnackbar({
        severity: "error",
        message: data.response.data,
      });
    },
  });

  return { cancelBooking, createBooking };
};

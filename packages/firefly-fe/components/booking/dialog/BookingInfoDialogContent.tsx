import { yupResolver } from "@hookform/resolvers/yup";
import { DoubleArrow } from "@mui/icons-material";
import { Box, Button, styled, Typography } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import Image from "next/image";
import { useEffect } from "react";
import { Controller, useForm } from "react-hook-form";
import { getImageUrl } from "../../../helpers/image";
import { useDialog } from "../../../providers/DialogProvider";
import { useMe } from "../../../providers/MeProvider";
import { Flex } from "../../commonStyledComponents";
import { TextInput } from "../../form/TextInput";
import { getCourseDetail } from "../query";
import { useBooking } from "../useBooking";
import { bookingInfoSchema } from "./bookingInfoValidationSchema";

interface BookingForm {
  name: string;
  email: string;
  phone: string;
  note: string;
}

interface Props {
  appointmentId: number | null;
}

export const BookingInfoDialogContent = ({ appointmentId }: Props) => {
  const { createBooking } = useBooking();
  const { me } = useMe();
  const { goBack, goTo, dialog } = useDialog();

  const {
    handleSubmit,
    control,
    formState: { errors },
    reset,
  } = useForm<BookingForm>({
    resolver: yupResolver(bookingInfoSchema),
    defaultValues: {
      email: me?.Email,
      name: me?.Name,
      phone: me?.Phone,
      note: "",
    },
  });

  useEffect(() => {
    reset({
      email: me?.Email,
      name: me?.Name,
      phone: me?.Phone,
      note: "",
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [me?.role]);

  const { data } = useQuery(
    ["courseDetail", { appointmentId: appointmentId! }],
    getCourseDetail
  );

  const onSubmit = (values: BookingForm) => {
    createBooking.mutate({
      courseId: data?.courseId!,
      appointmentId: dialog.appointmentId!,
      customerId: me?.Id!,
      customerNote: values.note || "",
      bookingServiceId: data?.bookingServiceId!,
      isAlternate: data?.freeSpaces === 0,
    });
  };

  const renderTitle = () => {
    if (data?.freeSpaces === 0) {
      return (
        <>
          Přihláška <br />
          náhradníka <br />
          na {data?.title}
        </>
      );
    }
    return (
      <>
        Přihláška na <br />
        {data?.title}
      </>
    );
  };

  const isLoggedIn = me?.role === "LoggedUser";
  const isAlternate = data?.freeSpaces === 0;

  return (
    <Container>
      <Box position="relative" mx={-4} height={300}>
        {(data?.coloredImage || data?.lectorImage || data?.image) && (
          <Image
            objectFit="cover"
            layout="fill"
            src={getImageUrl(data.coloredImage || data.lectorImage || data.image)}
            alt={data.title}
          />
        )}
      </Box>
      <BackLink
        color="secondary"
        variant="text"
        startIcon={<DoubleArrow />}
        onClick={goBack}
      >
        Zpět
      </BackLink>
      <Typography variant="h4" mt={4}>
        {renderTitle()}
      </Typography>
      {!me?.Email && (
        <Typography
          sx={{
            fontSize: "1rem",
            lineHeight: 1.75,
            color: "common.white",
            mt: 3,
          }}
        >
          <span>Máte uživatelský účet?&nbsp;&nbsp;</span>
          <RegistrationButton
            variant="text"
            color="secondary"
            sx={{ display: "context" }}
            onClick={() => goTo("login")}
            endIcon={<DoubleArrow />}
          >
            Přihlaš se
          </RegistrationButton>
        </Typography>
      )}
      <form onSubmit={handleSubmit(onSubmit)} key={me?.role}>
        <Flex flexDirection="column" mt={3}>
          <Controller
            control={control}
            name="name"
            render={({ field }) => (
              <TextInput
                {...field}
                variant="outlined"
                placeholder={isLoggedIn ? undefined : "Jméno"}
                label={!isLoggedIn ? undefined : "Jméno"}
                disabled={me?.role === "LoggedUser"}
                sx={{ marginBottom: 2, color: "common.white" }}
                helperText={errors.name?.message}
                InputLabelProps={{ shrink: true }}
              />
            )}
          />
          <Controller
            control={control}
            name="email"
            render={({ field }) => (
              <TextInput
                {...field}
                variant="outlined"
                placeholder={isLoggedIn ? undefined : "E-mail"}
                label={!isLoggedIn ? undefined : "E-mail"}
                disabled={me?.role === "LoggedUser"}
                type="email"
                sx={{ marginBottom: 2 }}
                helperText={errors.email?.message}
                InputLabelProps={{ shrink: true }}
              />
            )}
          />
          <Controller
            control={control}
            name="phone"
            render={({ field }) => (
              <TextInput
                {...field}
                variant="outlined"
                placeholder={isLoggedIn ? undefined : "Telefon"}
                label={!isLoggedIn ? undefined : "Telefon"}
                disabled={me?.role === "LoggedUser"}
                sx={{ marginBottom: 2 }}
                helperText={errors.phone?.message}
                inputRegex={/^[+\d\s]+$/}
                InputLabelProps={{ shrink: true }}
              />
            )}
          />
          {!isAlternate && (
            <Controller
              control={control}
              name="note"
              render={({ field }) => (
                <TextInput
                  {...field}
                  variant="outlined"
                  placeholder="Poznámka"
                  rows={2}
                  multiline
                  sx={{ marginBottom: 2 }}
                />
              )}
            />
          )}
        </Flex>
        <Button
          type="submit"
          color="secondary"
          variant="contained"
          size="large"
          disabled={createBooking.isLoading}
          fullWidth
          sx={{
            mt: 4,
          }}
        >
          Odeslat přihlášku
        </Button>
      </form>
      <Typography mt={3} fontSize="0.75rem">
        Vyplněné údaje považujeme za důvěrné. Ochrana osobních dat je zaručena,
        údaje neposkytujeme žádné třetí osobě.
      </Typography>
    </Container>
  );
};

const Container = styled(Box)`
  p,
  h4 {
    color: ${({ theme }) => theme.palette.common.white};
  }
`;

const BackLink = styled(Button)`
  font-size: 1.1rem;
  padding-left: 0;
  padding-right: 0;
  font-family: "HrotBasic";
  align-self: flex-start;
  margin-top: 24px;

  svg {
    transform: rotate(-180deg);
  }
`;

const RegistrationButton = styled(Button)`
  font-weight: normal;
  padding: 0;
  margin-top: -4px;
  letter-spacing: normal;
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen,
    Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;
`;

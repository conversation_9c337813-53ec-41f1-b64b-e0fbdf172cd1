import * as yup from "yup";

export const bookingInfoSchema = yup
  .object()
  .shape({
    name: yup.string().required("Toto pole je požadované"),
    email: yup
      .string()
      .required("Toto pole je požadované")
      .matches(
        /^\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/,
        "Špatný formát"
      ),
    phone: yup
      .string()
      .required("Toto pole je požadované")
      .matches(/^[+]*[(]{0,1}[0-9]{1,4}[)]{0,1}[-\s\./0-9]*$/, "Špatný formát"),
  })
  .required("Toto pole je požadované");

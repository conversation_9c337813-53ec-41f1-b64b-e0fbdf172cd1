import { DoubleArrow } from "@mui/icons-material";
import { Box, Button, styled, Typography } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import Image from "next/image";
import { sortBy, uniqBy } from "rambda";
import { getImageUrl } from "../../../helpers/image";
import { getFormattedDate, getFormattedTime } from "../../../helpers/time";
import { removeDiacritics } from "../../../helpers/url";
import { useDialog } from "../../../providers/DialogProvider";
import { useMe } from "../../../providers/MeProvider";
import { routes } from "../../../routes";
import Link from "../../Link";
import { getTimeFromUnixTimeStamp } from "../helper";
import { getCourseDetail } from "../query";
import { getReservations } from "../../profile/reservations/query";
import { LoadingPlaceholderIndicator } from "../../LoadingPlaceholderIndicator";

const mapDayText = [
  "neděli",
  "pondělí",
  "úterý",
  "středu",
  "čtvrtek",
  "pátek",
  "sobotu",
];

const mapEveryText = [
  "Každou",
  "Každé",
  "Každé",
  "Každou",
  "Každý",
  "Každý",
  "Každou",
];

interface Props {
  appointmentId: number | null;
}

export const BookingDialogContent = ({ appointmentId }: Props) => {
  const { goTo, close } = useDialog();
  const { me } = useMe();
  const { data: reservations } = useQuery(["reservations"], getReservations, {
    enabled: !!me?.Id,
  });

  const { data, isLoading } = useQuery(
    ["courseDetail", { appointmentId: appointmentId! }],
    getCourseDetail
  );

  const isAlreadyBooked = reservations?.find(
    (r) => r.AppointmentId === appointmentId
  );

  const dateFormat = getTimeFromUnixTimeStamp(data?.fromDate || 0);
  const date = getFormattedDate(dateFormat, { year: true });
  const isCourse = (data?.totalLessons || 0) > 1;

  const renderLessonDays = () => {
    const lessonDatesDateFormat = data?.lessonDates.map((d) => new Date(d));
    const lessonDays = uniqBy((d) => {
      return `${d.getDay()}-${d.getHours()}`;
    }, lessonDatesDateFormat || []);
    const lessonDaysSortedByDayNumber = sortBy((a) => a.getDay(), lessonDays);

    return (
      <>
        {mapEveryText[lessonDaysSortedByDayNumber[0]?.getDay()]}{" "}
        {lessonDaysSortedByDayNumber
          .map((day) => `${mapDayText[day.getDay()]} ${getFormattedTime(day)}`)
          .join(" a ")}
      </>
    );
  };

  const renderDate = () => {
    const lessonDate = data?.lessonDates?.[0];
    if (!lessonDate) {
      return null;
    }

    if (isCourse) {
      return (
        <Typography mt={3}>
          <b>Kurz běží od: </b>
          {data && date}
        </Typography>
      );
    }
    return (
      <Typography mt={3}>
        <b>Lekce se koná: </b>
        {date} v {getFormattedTime(new Date(lessonDate))}
      </Typography>
    );
  };

  const handleGoNext = () => {
    if (me?.role === "LoggedUser") {
      goTo("bookingInfo", { appointmentId });
    } else {
      goTo("login");
    }
  };

  const renderLoading = (width: number | string, height: number) => (
    <LoadingPlaceholderIndicator
      transparent
      sx={{
        width,
        height,
        borderRadius: 2,
      }}
    />
  );

  if (isLoading) {
    return (
      <Container>
        {renderLoading("100%", 300)}{" "}
        <Box mt={2}>{renderLoading("100%", 80)}</Box>
        <Box mt={2}>{renderLoading("100%", 120)}</Box>
        <Box mt={2}>{renderLoading("100%", 200)}</Box>
      </Container>
    );
  }

  const isFull = data?.freeSpaces === 0;

  console.log(data)

  return (
    <Container>
      <Box position="relative" mx={-4} height={300}>
        {(data?.coloredImage || data?.lectorImage || data?.image) && (
          <Image
            objectFit="cover"
            layout="fill"
            src={getImageUrl(data.coloredImage || data.lectorImage || data.image)}
            alt={data.title}
          />
        )}
      </Box>
      <Typography variant="h4" mt={4}>
        {data?.title}
      </Typography>
      <Typography mt={4}>{data?.description}</Typography>
      {data?.lectorName !== "- bez přítomnosti lektora" && (
        <Typography mt={4}>
          <b>Instruktor: </b>
          <Link
            href={`${routes.lectors}/${removeDiacritics(data?.lectorName || "")
              .split(" ")
              .join("-")
              .toLocaleLowerCase()}`}
            color="secondary"
            onClick={() => close({ keepParams: true })}
            endIcon={<DoubleArrow />}
            sx={{ display: "inline-flex" }}
          >
            {data?.lectorName}
          </Link>
        </Typography>
      )}
      {renderDate()}
      {isCourse && (
        <Typography mt={1}>
          <b>{renderLessonDays()}</b>
        </Typography>
      )}
      <Typography mt={1}>
        <b>Kde: </b>
        {data?.locationName}
      </Typography>
      <Typography mt={3}>
        <b>Volná místa: </b>
        {data?.freeSpaces}
      </Typography>
      {isFull && (
        <Typography fontWeight={700}>
          {isCourse ? "Tento kurz" : "Tato lekce"} už má bohužel plno. Pokud se
          přihlásíš jako náhradník, dáme ti vědět, když se někdo odhlásí!
        </Typography>
      )}
      <Typography mt={3}>
        <Typography
          fontWeight="bold"
          fontSize="1.5rem"
          color="common.white"
          display="inline"
          component="span"
          mr={1}
        >
          {data?.totalPrice} Kč /
        </Typography>
        {data?.totalLessons} {(data?.totalLessons || 0) < 5 ? "lekce" : "lekcí"}
      </Typography>
      {isAlreadyBooked ? (
        <Typography
          variant="h6"
          color="white"
          sx={{ textAlign: "center", mt: 4 }}
        >
          Již jste přihlášeni
        </Typography>
      ) : (
        <Button
          onClick={handleGoNext}
          color="secondary"
          variant="contained"
          size="large"
          fullWidth
          sx={{
            mt: 4,
          }}
        >
          {isFull ? "Přihlásit jako náhradník" : "Přihlásit se"}
        </Button>
      )}
    </Container>
  );
};

const Container = styled(Box)`
  p,
  h4 {
    color: ${({ theme }) => theme.palette.common.white};
  }
`;

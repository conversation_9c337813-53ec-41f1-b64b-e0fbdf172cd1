import { <PERSON>, Button, styled, Typography } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import Image from "next/image";
import { getImageUrl } from "../../../helpers/image";
import { useDialog } from "../../../providers/DialogProvider";
import { useMe } from "../../../providers/MeProvider";
import { AlternateEvent, Event } from "../../../reservanto/types";
import { getReservations } from "../../profile/reservations/query";
import { bankInfo, getAmountToPayV2, getVS } from "../payment/helpers";
import { QrCode } from "../payment/QrCode";
import { getCourseDetail } from "../query";

interface Props {
  appointmentId: number | null;
}

export const BookingDoneDialogContent = ({ appointmentId }: Props) => {
  const { close } = useDialog();
  const { me } = useMe();

  const { data } = useQuery(
    ["courseDetail", { appointmentId: appointmentId! }],
    getCourseDetail
  );

  const { data: reservations } = useQuery(["reservations"], getReservations, {
    enabled: !!me?.Id,
  });

  const reservation = reservations?.find(
    (r) => r.AppointmentId === appointmentId
  );

  const reservedLessons: (Event | AlternateEvent)[] = [];
  if (reservation?.CourseId && reservation && reservations) {
    reservedLessons.push(
      ...reservations.filter((r) => r.CourseId === reservation.CourseId)
    );
  } else {
    reservation && reservedLessons.push(reservation);
  }

  const totalPrice = reservedLessons.reduce(
    (prev, l) => prev + (l as Event).Price || 0,
    0
  );

  const paymentInfo = data?.locationName && bankInfo[data.locationName];
  const amountToPay = getAmountToPayV2(
    data?.totalPrice,
    data?.lessonDates.length
  );

  const isCourse = (data?.totalLessons || 0) > 1;

  const renderBookedAsAlternateInfo = () => (
    <Typography my={4} color="common.white">
      Na e-mail jsme ti poslali shrnutí s konkrétními informacemi jak dále
      postupovat a zajistit si v kurzu místo.
    </Typography>
  );

  const renderBookedReservationInfo = () => (
    <>
      <Typography my={4} variant="h5" color="common.white">
        Platba:
      </Typography>
      {isCourse ? (
        totalPrice > 500 ? (
          <Typography my={4}>
            Zálohu ve výši 500 Kč uhraď nyní pomocí QR kódu nebo do 5 dnů dle instrukcí ve tvém uživatelském profilu - sekce rezervace. Shrnutí jsme ti poslali také na e-mail.
          </Typography>
        ) : (
          <Typography my={4}>
            Platbu uhraď pomocí QR kódu nebo dle instrukcí ve tvém
            uživatelském profilu - sekce rezervace. Shrnutí jsme ti poslali
            také na e-mail.
          </Typography>
        )
      ) : (
        <Typography my={4}>
          Platbu uhraď pomocí QR kódu nebo dle instrukcí ve tvém uživatelském
          profilu - sekce rezervace. Pro uplatnění náhrady vyber v
          uživatelském profilu formu úhrady - náhradou. Shrnutí jsme ti poslali
          také na e-mail.
        </Typography>
      )}

      <QrCode
        amount={amountToPay}
        bankCode={paymentInfo?.bankCode}
        message={paymentInfo?.message}
        accountNumber={paymentInfo?.accountNumber}
        variableSymbol={getVS(me?.Phone)}
      />
    </>
  );

  return (
    <Container>
      <Box position="relative" mx={-4} height={300}>
        {(data?.coloredImage || data?.lectorImage || data?.image) && (
          <Image
            objectFit="cover"
            layout="fill"
            src={getImageUrl(data.coloredImage || data.lectorImage || data.image)}
            alt={data.title}
          />
        )}
      </Box>
      <Typography variant="h4" mt={4}>
        Přihláška na <br />
        {data?.title}
      </Typography>
      <Typography mt={4}>
        Pokud se nás chceš na něco zeptat, neváhej a kontaktuj nás.
      </Typography>
      {reservation?.Status === "waiting"
        ? renderBookedAsAlternateInfo()
        : reservation && !reservation?.IsPaid
          ? renderBookedReservationInfo()
          : reservation && (
            <Typography my={4}>Přihláška byla uhrazena pernamentkou</Typography>
          )}
      <Button
        onClick={() => close()}
        color="secondary"
        variant="contained"
        size="large"
        fullWidth
        sx={{
          mt: 4,
        }}
      >
        Zavřít
      </Button>
    </Container>
  );
};

const Container = styled(Box)`
  p,
  h4 {
    color: ${({ theme }) => theme.palette.common.white};
  }
`;

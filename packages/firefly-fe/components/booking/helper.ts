import { groupBy } from "rambda";
import { Class } from "../../reservanto/types";
import moment from "moment-timezone";

export const getClassesByDates = groupBy(
  (c: Class) => `${c.StartDate}-${c.EndDate}`
);

export const getWeekDays = (current: Date) => {
  const day = current;
  let week: Date[] = [];

  const currentDay = day.getDay() === 0 ? 6 : day.getDay() - 1;
  // Starting Monday not Sunday
  day.setDate(day.getDate() - currentDay);
  for (let i = 0; i < 7; i++) {
    // let's start in the beginning of day, resetting seconds / minutes / milliseconds is done on server because of caching
    day.setHours(0);
    week.push(new Date(day));
    day.setDate(day.getDate() + 1);
  }
  return week;
};

export const getWorkingTimes = (current: Date) => {
  const time = current;
  let workingHours: Date[] = [];

  time.setHours(7);
  time.setMinutes(0);
  time.setSeconds(0);
  time.setMilliseconds(0);

  for (let day = 0; day < 7; day++) {
    for (let halfHour = 0; halfHour < 30; halfHour++) {
      workingHours.push(new Date(time));
      const newMinutes = time.getMinutes() + 30;
      const isNewHour = newMinutes === 60;
      time.setMinutes(newMinutes % 60);
      if (isNewHour) {
        time.setHours(time.getHours() + 1);
      }
    }
    time.setDate(time.getDate() + 1);
    time.setHours(7);
    time.setMinutes(0);
    time.setSeconds(0);
    time.setMilliseconds(0);
  }
  return workingHours;
};

export const getDayHours = (current: Date) => {
  const time = current;
  let day: Date[] = [];

  time.setHours(7);
  time.setMinutes(0);
  time.setSeconds(0);
  time.setMilliseconds(0);
  for (let i = 0; i < 30; i++) {
    day.push(new Date(time));
    const newMinutes = time.getMinutes() + 30;
    const isNewHour = newMinutes === 60;
    time.setMinutes(newMinutes % 60);
    if (isNewHour) {
      time.setHours(time.getHours() + 1);
    }
  }
  return day;
};

export const getTimeInUnixTimeStamp = (date: Date) => {
  return date.getTime() / 1000;
};

export const getTimeFromUnixTimeStamp = (date: number) => {
  return new Date(date * 1000);
};

export const getTime = (startTime: number, endTime: number) => {
  const startDate = getTimeFromUnixTimeStamp(startTime);
  const endDate = getTimeFromUnixTimeStamp(endTime);
  const startDateInPrague = moment.tz(startDate.toISOString(), "Europe/Prague");
  const endDateInPrague = moment.tz(endDate.toISOString(), "Europe/Prague");

  const lessonLengthInMinutes = Math.floor(endTime - startTime) / 60;

  return {
    startDay: startDateInPrague.day(),
    startHours: startDateInPrague.hours(),
    startMinutes: startDateInPrague.minutes(),
    endDay: endDateInPrague.day(),
    endHours: endDateInPrague.hours(),
    endMinutes: endDateInPrague.minutes(),
    lessonLength: lessonLengthInMinutes,
  };
};

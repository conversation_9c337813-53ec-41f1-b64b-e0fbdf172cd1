import { Box, styled, SxProps, Theme } from "@mui/material";
import { Flex } from "../../commonStyledComponents";

interface Props {
  sx?: SxProps<Theme>;
  accountNumber: number;
  bankCode: number;
  amount: number;
  variableSymbol: string;
  message: string;
}

export const QrCode = ({
  sx,
  accountNumber,
  bankCode,
  amount,
  variableSymbol,
  message,
}: Props) => {
  const qrCodeLink = `http://api.paylibo.com/paylibo/generator/czech/image?accountNumber=${accountNumber}&bankCode=${bankCode}&amount=${amount}&currency=CZK&vs=${variableSymbol}&message=${message}`;
  return (
    <Flex justifyContent="center" position="relative" overflow="hidden" sx={sx}>
      <img src={qrCodeLink} alt="QR code" style={{ borderRadius: "32px" }} />
      <QrBorder />
    </Flex>
  );
};

const QrBorder = styled(Box)`
  position: absolute;
  width: 270px;
  height: 300px;
  border: ${({ theme }) => `40px solid ${theme.palette.common.white}`};
  border-radius: 32px;
`;

import { Button } from "@mui/material";
import { Flex } from "../../commonStyledComponents";

interface Props {
  onPay: (type: "credit" | "replacement") => void;
}

export const ReplacementPayment = ({ onPay }: Props) => {
  return (
    <Flex
      flexDirection="column"
      justifyContent="center"
      height="100%"
      alignItems="center"
      flex={1}
    >
      <Button
        variant="outlined"
        sx={{ alignSelf: "center" }}
        onClick={() => onPay("replacement")}
      >
        Zaplatit náhradou
      </Button>
    </Flex>
  );
};

import { Typography } from "@mui/material";
import { useDialog } from "../../../providers/DialogProvider";
import { useMe } from "../../../providers/MeProvider";
import { Flex } from "../../commonStyledComponents";
import { bankInfo, getVS } from "./helpers";
import { QrCode } from "./QrCode";

export const QrCodePayment = () => {
  const { dialog } = useDialog();
  const { me } = useMe();

  const { accountNumber, bankCode, message } =
    bankInfo[dialog.payment?.type || "credit"];
  const vs = getVS(me?.Phone);

  const amount = dialog.payment?.amount || 0;

  return (
    <>
      <QrCode
        sx={{ mt: -3 }}
        amount={amount}
        variableSymbol={vs}
        message={message}
        bankCode={bankCode}
        accountNumber={accountNumber}
      />
      <Flex
        flexDirection={{ xs: "column", sm: "row" }}
        mt={2}
        alignItems={{ xs: "flex-start", sm: "center" }}
      >
        <Flex
          flexDirection={{ sm: "column" }}
          mt={{ xs: 2, sm: 0 }}
          justifyContent="space-between"
          width="100%"
        >
          <Typography fontSize={{ xs: "0.75rem", sm: "0.875rem" }}>
            Číslo účtu:
          </Typography>
          <Typography
            fontWeight={800}
            mt={{ sm: 2 }}
            ml={{ xs: 2, sm: 0 }}
            fontSize={{ xs: "0.75rem", sm: "1rem" }}
          >
            {accountNumber}/{bankCode}
          </Typography>
        </Flex>
        <Flex
          flexDirection={{ sm: "column" }}
          ml={{ sm: 5 }}
          mt={{ xs: 2, sm: 0 }}
          justifyContent="space-between"
          width="100%"
        >
          <Typography
            width={{ xs: "100px", sm: "inherit" }}
            fontSize={{ xs: "0.75rem", sm: "0.875rem" }}
          >
            Částka:
          </Typography>
          <Typography
            mt={{ sm: 2 }}
            ml={{ xs: 2, sm: 0 }}
            fontSize={{ xs: "0.75rem", sm: "1rem" }}
          >
            {amount} Kč
          </Typography>
        </Flex>
        <Flex
          flexDirection={{ sm: "column" }}
          ml={{ sm: 5 }}
          mt={{ xs: 2, sm: 0 }}
          justifyContent="space-between"
          width="100%"
        >
          <Typography
            fontSize={{ xs: "0.75rem", sm: "0.875rem" }}
            whiteSpace="nowrap"
          >
            Variabilní symbol:
          </Typography>
          <Typography
            mt={{ sm: 2 }}
            ml={{ xs: 2, sm: 0 }}
            fontSize={{ xs: "0.75rem", sm: "1rem" }}
          >
            {vs}
          </Typography>
        </Flex>
      </Flex>
    </>
  );
};

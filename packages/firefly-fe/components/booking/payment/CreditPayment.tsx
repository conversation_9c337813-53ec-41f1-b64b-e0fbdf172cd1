import { Button, Typography } from "@mui/material";
import { useDialog } from "../../../providers/DialogProvider";
import { useMe } from "../../../providers/MeProvider";
import { Flex } from "../../commonStyledComponents";

interface Props {
  onPay: (type: "credit" | "replacement", amount: number) => void;
}

export const CreditPayment = ({ onPay }: Props) => {
  const { me } = useMe();
  const { dialog } = useDialog();

  const amount = dialog.payment?.amount || 0;

  return (
    <Flex
      flexDirection="column"
      justifyContent="center"
      height="100%"
      alignItems="center"
    >
      <Flex
        flexDirection={{ xs: "column", sm: "row" }}
        mt={2}
        alignItems={{ xs: "flex-start", sm: "center" }}
        justifyContent="space-between"
      >
        <Flex
          flexDirection={{ sm: "column" }}
          mt={{ xs: 2, sm: 0 }}
          justifyContent="space-between"
          width="100%"
          minWidth={70}
        >
          <Typography
            width={{ xs: "100px", sm: "inherit" }}
            fontSize={{ xs: "0.75rem", sm: "0.875rem" }}
            whiteSpace="nowrap"
          >
            Váš kredit:
          </Typography>
          <Typography
            mt={{ sm: 3 }}
            ml={{ xs: 2, sm: 0 }}
            fontSize="1rem"
            fontWeight={700}
          >
            {me?.Credit} Kč
          </Typography>
        </Flex>
        <Flex
          flexDirection={{ sm: "column" }}
          mt={{ xs: 2, sm: 0 }}
          justifyContent="space-between"
          width="100%"
          ml={{ sm: 10 }}
        >
          <Typography
            width={{ xs: "100px", sm: "inherit" }}
            fontSize={{ xs: "0.75rem", sm: "0.875rem" }}
            whiteSpace="nowrap"
          >
            Částka:
          </Typography>
          <Typography mt={{ sm: 3 }} ml={{ xs: 2, sm: 0 }} fontSize="1rem">
            {amount} Kč
          </Typography>
        </Flex>
      </Flex>
      <Button
        variant="outlined"
        disabled={amount > (me?.Credit || 0)}
        sx={{ alignSelf: "center", mt: 5 }}
        onClick={() => onPay("credit", amount)}
      >
        Zaplatit kreditem
      </Button>
    </Flex>
  );
};

import { axiosApi } from "../../../axios";

export interface PayInput {
  appointmentId: number;
  type: "credit" | "replacement" | "seasonPass";
  amount?: number;
  passUsageId?: number;
}

export const payReservation = async (input: PayInput): Promise<void> => {
  const { appointmentId, type, amount, passUsageId } = input;
  const response = await axiosApi({
    method: "put",
    url: `/reservations/${appointmentId}/pay`,
    params: {
      type,
      amount,
      passUsageId
    },
  });
  return response.data;
};

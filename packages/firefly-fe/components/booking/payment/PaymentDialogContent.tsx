import { Tab, Tabs } from "@mui/material";
import { useState } from "react";
import { useDialog } from "../../../providers/DialogProvider";
import { useSnackbar } from "../../../providers/SnackbarProvider";
import { Flex } from "../../commonStyledComponents";
import { useReplacements } from "../../profile/replacements/useReplacements";
import { CreditPayment } from "./CreditPayment";
import { QrCodePayment } from "./QrCodePayment";
import { ReplacementPayment } from "./ReplacementPayment";
import { useMakePayment } from "./useMakePayment";

function a11yProps(index: number) {
  return {
    id: `simple-tab-${index}`,
    "aria-controls": `simple-tabpanel-${index}`,
  };
}

export const PaymentDialogContent = () => {
  const {
    dialog: { appointmentId, bookingServiceId, courseId },
    close,
  } = useDialog();
  const [activeTab, setActiveTab] = useState(0);

  const makePayment = useMakePayment();
  const { showSnackbar } = useSnackbar();

  const handlePay = (type: "credit" | "replacement", amount?: number) => {
    makePayment.mutate(
      {
        type,
        amount,
        appointmentId: appointmentId!,
      },
      {
        onSuccess: () => {
          showSnackbar({
            severity: "success",
            message: "Rezervace byla zaplacena",
          });
          close();
        },
      }
    );
  };

  const { canApplyReplacement } = useReplacements(
    {
      bookingServiceId: bookingServiceId!,
      courseId,
    },
    { canApplyReplacement: true }
  );

  return (
    <Flex alignItems="center" justifyContent="center" flexDirection="column">
      {appointmentId && (
        <Tabs
          value={activeTab}
          onChange={(_, tab) => setActiveTab(tab)}
          aria-label="Payment tabs"
        >
          <Tab label="Převodem" {...a11yProps(0)} />
          <Tab label="Kreditem" {...a11yProps(1)} />
          {canApplyReplacement && <Tab label="Náhradou" {...a11yProps(2)} />}
        </Tabs>
      )}

      <TabPanel value={activeTab} index={0}>
        <QrCodePayment />
      </TabPanel>
      <TabPanel value={activeTab} index={1}>
        <CreditPayment onPay={handlePay} />
      </TabPanel>
      {canApplyReplacement && (
        <TabPanel value={activeTab} index={2}>
          <ReplacementPayment onPay={handlePay} />
        </TabPanel>
      )}
    </Flex>
  );
};

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Flex
          mt={3}
          height={{ sm: 370 }}
          minHeight={200}
          flexDirection="column"
        >
          {children}
        </Flex>
      )}
    </div>
  );
};

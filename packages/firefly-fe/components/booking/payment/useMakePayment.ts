import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useMe } from "../../../providers/MeProvider";
import { payReservation } from "./query";

export const useMakePayment = () => {
  const me = useMe();
  const client = useQueryClient();

  return useMutation(payReservation, {
    onSuccess: () => {
      me.refetch();
      client.invalidateQueries(["reservations"]);
      client.invalidateQueries(["replacements"]);
      client.invalidateQueries(["creditTransactions"]);
      client.invalidateQueries(["courseCustomerEvents"]);
    },
  });
};

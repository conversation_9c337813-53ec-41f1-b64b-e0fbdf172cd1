import { Customer, Event } from "../../../reservanto/types";

export const getAmountToPay = (lessons: Event[]) => {
  const totalPrice = lessons.reduce((prev, l) => l.Price + prev, 0);
  const totalPayed = lessons.reduce((prev, l) => l.PaidPartOfPrice + prev, 0);

  return lessons.length <= 2
    ? totalPrice
    : totalPayed === 0
    ? 500
    : totalPrice - totalPayed;
};

export const getAmountToPayV2 = (
  totalPrice?: number,
  lessonsCount?: number,
  totalPayed?: number
) => {
  totalPayed = totalPayed || 0;
  lessonsCount = lessonsCount || 0;
  totalPrice = totalPrice || 0;

  return lessonsCount <= 2
    ? totalPrice
    : totalPayed === 0
    ? 500
    : totalPrice - totalPayed;
};

export const getVS = (phone?: string) => {
  phone = phone || "";
  return phone.replaceAll(" ", "").substring(phone.length - 9, phone.length);
};

export const bankInfo = {
  credit: {
    accountNumber: **********,
    bankCode: 2010,
    message: "Dobití kreditu",
  },
  "I. P. Pavlova - SÁL 1": {
    accountNumber: **********,
    bankCode: 2010,
    message: "Platba",
  },
  "Studio Hradčanská": {
    accountNumber: **********,
    bankCode: 2010,
    message: "Platba",
  },
  "I.P. Pavlova - SÁL 2": {
    accountNumber: **********,
    bankCode: 2010,
    message: "Platba",
  },
};

export const availablePastoPay = (bookingServiceId: number, me: Customer) => {
  let availablePass: null | number = null;
  me.BoughtPasses?.forEach((pass) =>
    pass.Usages.forEach((usage) => {
      if (usage.BookingServiceId === bookingServiceId) {
        availablePass = usage.PassUsageId;
      }
    })
  );
  return availablePass;
};

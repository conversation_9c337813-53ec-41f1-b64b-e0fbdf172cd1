import { Box, styled, Typography } from "@mui/material";
import Tab from "@mui/material/Tab";
import Tabs from "@mui/material/Tabs";

interface StyledTabsProps {
  children?: React.ReactNode;
  value: number;
  onChange: (event: React.SyntheticEvent, newValue: number) => void;
}

const StyledTabs = styled((props: StyledTabsProps) => (
  <Tabs
    {...props}
    TabIndicatorProps={{ children: <span className="MuiTabs-indicatorSpan" /> }}
  />
))(({ theme }) => ({
  backgroundColor: theme.palette.common.white,
  borderRadius: "3rem",
  width: "fit-content",
  "& .MuiTabs-indicator": {
    display: "flex",
    justifyContent: "center",
    backgroundColor: "transparent",
  },
  "& .MuiTabs-indicatorSpan": {
    display: "none",
  },
}));

interface StyledTabProps {
  label: React.ReactNode;
}

const StyledTab = styled((props: StyledTabProps) => (
  <Tab disableRipple {...props} />
))(({ theme }) => ({
  textTransform: "none",
  backgroundColor: theme.palette.common.white,
  borderRadius: "3rem",
  fontWeight: theme.typography.fontWeightBold,
  color: theme.palette.common.black,
  transition: "0.3s",
  "&.Mui-selected": {
    color: theme.palette.common.black,
    backgroundColor: theme.palette.secondary.main,
  },
  "&.Mui-focusVisible": {
    backgroundColor: "rgba(100, 95, 228, 0.32)",
  },
}));

interface SwitchLabelProps {
  label: string;
  subLabel: string;
}

const SwitchLabel = ({ label, subLabel }: SwitchLabelProps) => {
  return (
    <Box>
      <Typography textAlign="start" fontSize={{ xs: "0.5rem", md: "0.75rem"}}>
        {subLabel}
      </Typography>
      <Typography
        fontFamily="HrotBasic"
        sx={{
          fontSize: { xs: "0.75rem", md: "1.25rem" },
          fontWeight: 900,
        }}
      >
        {label}
      </Typography>
    </Box>
  );
};

interface Props {
  items: {
    subLabel: string;
    label: string;
    value: any;
  }[];
  activeIndex: any;
  onChange: (event: React.SyntheticEvent, newIndex: number) => void;
}

export const Switch = ({ items, activeIndex, onChange }: Props) => {
  return (
    <StyledTabs
      value={activeIndex}
      onChange={onChange}
      sx={{
        width: { xs: "100%", md: "fit-content" },
      }}
    >
      {items.map((item) => (
        <StyledTab
          sx={{
            p: { xs: "0.75rem 1rem", md: "0.75rem 2rem" },
            width: { xs: `${100 / items.length}%`, md: "auto" },
          }}
          key={item.value}
          label={<SwitchLabel label={item.label} subLabel={item.subLabel} />}
        />
      ))}
    </StyledTabs>
  );
};

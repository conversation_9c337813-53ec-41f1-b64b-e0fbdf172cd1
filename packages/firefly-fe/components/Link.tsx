import { Link as MUILink, styled } from "@mui/material";
import {
  DefaultComponentProps,
  OverridableTypeMap,
} from "@mui/material/OverridableComponent";
import NextLink from "next/link";
import { forwardRef } from "react";
import { Flex } from "./commonStyledComponents";
import { resetScroll } from "../helpers/scroll";

interface Props extends DefaultComponentProps<OverridableTypeMap> {
  children?: React.ReactNode;
  href: string;
  endIcon?: React.ReactNode;
  startIcon?: React.ReactNode;
  onClickResetHorizontal?: boolean;
}

const Link = forwardRef(
  (
    {
      href,
      children,
      variant,
      endIcon,
      startIcon,
      onClickResetHorizontal,
      ...rest
    }: Props,
    ref: any
  ) => {
    const handleClick = () => {
      resetScroll();
    };

    return (
      <NextLink href={href} passHref>
        <StyledMUILink
          variant={variant || "body2"}
          {...rest}
          ref={ref}
          onClick={onClickResetHorizontal ? () => handleClick() : rest.onClick}
        >
          {startIcon && (
            <Flex mr={2} alignSelf="center">
              {startIcon}
            </Flex>
          )}
          {children}
          {endIcon && (
            <Flex ml={"0.4em"} alignSelf="center">
              {endIcon}
            </Flex>
          )}
        </StyledMUILink>
      </NextLink>
    );
  }
);

Link.displayName = "Link";

export default Link;

const StyledMUILink = styled(MUILink)`
  display: flex;
  align-items: center;
`;

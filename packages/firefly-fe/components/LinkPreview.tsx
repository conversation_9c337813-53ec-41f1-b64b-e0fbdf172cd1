interface Props {
  title?: string;
  description?: string;
  image?: string;
  siteUrl?: string;
}

export const LinkPreview = ({ description, title, image, siteUrl }: Props) => (
  <>
    <meta property="og:title" content={title} />
    <meta property="og:description" content={description} />
    <meta property="og:image" content={image || "/firefly-icon-logo.png"} />
    <meta property="og:site_name" content="Firefly Studio" />
    <meta property="og:url" content={siteUrl} />
  </>
);

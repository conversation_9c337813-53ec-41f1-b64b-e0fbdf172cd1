import { Box, styled, SxProps, Theme } from "@mui/material";
import { useChangeColorOn } from "../../helpers/animation";
import Link from "../Link";

interface Props {
  title: string;
  href: string;
  changeLinkColorOnDiv?: string;
  sx?: SxProps<Theme> | undefined;
}

export const HeaderLink = ({
  title,
  href,
  changeLinkColorOnDiv,
  sx,
}: Props) => {
  const { itemRef, color } = useChangeColorOn({
    onDivId: changeLinkColorOnDiv,
    fromColor: "primary.main",
    toColor: "background.default",
  });

  return (
    <Box ref={itemRef} sx={sx}>
      <StyledLink href={href} sx={{ color }} onClickResetHorizontal>
        {title}
      </StyledLink>
    </Box>
  );
};

const StyledLink = styled(Link)`
  ${({ theme }) => ({
    [theme.breakpoints.down("sm")]: {
      color: theme.palette.primary.main,
    },
  })}
`;

import DensityLargeIcon from "@mui/icons-material/DensityLarge";
import {
  Box,
  Button,
  IconButton,
  Link,
  List,
  ListItem,
  styled,
  SwipeableDrawer,
} from "@mui/material";
import { useState } from "react";
import Logo from "../../assets/firefly-icon-logo.svg";
import { useDialog } from "../../providers/DialogProvider";
import { useMe } from "../../providers/MeProvider";
import { routes } from "../../routes";
import { Flex } from "../commonStyledComponents";
import { HeaderResponse } from "./types";

interface Props {
  headerData: HeaderResponse | undefined;
}

const MobileMenu = ({ headerData }: Props) => {
  const [isOpen, setIsOpen] = useState(false);
  const { goTo } = useDialog();
  const { me, logout } = useMe();

  const handleOpenLogin = () => {
    goTo("login");
    setIsOpen(false);
  };

  const handleLogout = () => {
    logout();
    setIsOpen(false);
  };

  return (
    <>
      <IconButton
        onClick={() => setIsOpen((prevState) => !prevState)}
        sx={{ color: "primary.main" }}
      >
        <DensityLargeIcon />
      </IconButton>
      <MenuListContainer>
        <SwipeableDrawer
          anchor="bottom"
          open={isOpen}
          onOpen={() => setIsOpen(true)}
          onClose={() => setIsOpen(false)}
        >
          <List>
            {headerData?.activityLinks.map((link) => (
              <ListItem key={link.slug} sx={{ justifyContent: "flex-end" }}>
                <ItemLink href={`${routes.activities}/${link.slug}`}>
                  {link.title}
                </ItemLink>
              </ListItem>
            ))}
            {headerData?.otherLinks.map((link) => (
              <ListItem key={link.slug} sx={{ justifyContent: "flex-end" }}>
                <ItemLink href={`/${link.slug}`}>{link.title}</ItemLink>
              </ListItem>
            ))}
            <LogoLoginItem>
              <Flex alignItems="center" gap={2}>
                <Logo />
                <Link
                  href="/info"
                  sx={{
                    color: "common.white",
                    fontWeight: 700,
                    fontSize: "1.2rem",
                  }}
                >
                  EN
                </Link>
              </Flex>
              {me?.role === "LoggedUser" ? (
                <>
                  <Button
                    variant="outlined"
                    color="info"
                    onClick={handleLogout}
                    size="medium"
                  >
                    Odhlásit se
                  </Button>
                  <Button
                    variant="outlined"
                    color="info"
                    component={Link}
                    size="medium"
                    href={routes.profile}
                  >
                    Profil
                  </Button>
                </>
              ) : (
                <Button
                  variant="outlined"
                  color="info"
                  size="medium"
                  onClick={handleOpenLogin}
                >
                  Přihlášení
                </Button>
              )}
            </LogoLoginItem>
          </List>
        </SwipeableDrawer>
      </MenuListContainer>
    </>
  );
};

export default MobileMenu;

const MenuListContainer = styled(Box)`
  position: absolute;
  background: ${({ theme }) => theme.palette.background.paper};
  left: 0;
  top: 0;
  right: 0;
`;

const ItemLink = styled(Link)`
  font-weight: 700;
  padding: 12px 24px;
  color: ${({ theme }) => theme.palette.background.default};
  font-size: 1.2rem;
`;

const LogoLoginItem = styled(ListItem)`
  justify-content: space-between;
  svg {
    color: ${({ theme }) => theme.palette.text.secondary};
  }
`;

import { Box, Button, styled, useTheme } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import Image from "next/image";
import Logo from "../../assets/firefly-logo.svg";
import { useChangeColorOn } from "../../helpers/animation";
import { useDialog } from "../../providers/DialogProvider";
import { useMe } from "../../providers/MeProvider";
import { routes } from "../../routes";
import { Flex } from "../commonStyledComponents";
import Link from "../Link";
import { HeaderLink } from "./HeaderLink";
import MobileMenu from "./MobileMenu";
import { getHeader } from "./query";

interface Props {
  changeLinkColorOnDiv?: string;
}

const Header = ({ changeLinkColorOnDiv }: Props) => {
  const { data: header } = useQuery(["header"], getHeader);
  const { me } = useMe();
  const { palette } = useTheme();
  const { goTo } = useDialog();

  const { itemRef, color } = useChangeColorOn({
    onDivId: changeLinkColorOnDiv,
    fromColor: palette.common.black,
    toColor: palette.background.default,
  });

  return (
    <HeaderC>
      <HeaderContainer
        justifyContent={{ xs: "center", lg: "space-between" }}
        flexDirection={{ xs: "column", lg: "row" }}
        alignItems={{ xs: "flex-start", lg: "center" }}
      >
        <Link href="/" ref={itemRef}>
          <ColoredLogo
            color={changeLinkColorOnDiv ? color : palette.common.black}
          />
        </Link>
        <Flex
          gap={{ sm: 3, xl: 5 }}
          mt={{ xs: 5, lg: 0 }}
          alignItems="center"
          display={{ xs: "none", sm: "flex" }}
          justifyContent={{ sm: "space-between", lg: "flex-start" }}
          width={{ xs: "100%", lg: "auto" }}
        >
          {header?.data?.attributes?.activityLinks?.map((link) => (
            <HeaderLink
              key={link.slug}
              href={`${routes.activities}/${link.slug}`}
              sx={{
                width: {
                  xs: `${
                    100 / header?.data?.attributes?.activityLinks.length
                  }%`,
                  md: "auto",
                },
              }}
              title={link.title}
              changeLinkColorOnDiv={changeLinkColorOnDiv}
            />
          ))}
        </Flex>
        <Flex
          gap={{ sm: 1, md: 3, xl: 5 }}
          alignItems="center"
          display={{ xs: "none", lg: "flex" }}
        >
          {header?.data?.attributes?.otherLinks?.map((link) => (
            <HeaderLink
              key={link.slug}
              href={`/${link.slug}`}
              title={link.title}
              changeLinkColorOnDiv={changeLinkColorOnDiv}
            />
          ))}
          {me?.role === "LoggedUser" ? (
            <Button
              variant="outlined"
              color="primary"
              component={Link}
              href={routes.profile}
              size="medium"
            >
              Profil
            </Button>
          ) : (
            <Button
              variant="outlined"
              color="primary"
              onClick={() => goTo("login")}
              size="medium"
            >
              Přihlášení
            </Button>
          )}
          <Link href="/info" sx={{ mx: { xs: 0, xl: -3 } }}>
            EN
          </Link>
          <Box sx={{ display: { xs: "none", lg: "block" } }}>
            <Image
              src="/firefly-icon-logo.svg"
              alt="Firefly icon logo"
              width={65}
              height={64}
            />
          </Box>
        </Flex>
        <MobileMenuContainer display={{ xs: "block", lg: "none" }}>
          <MobileMenu headerData={header?.data.attributes} />
        </MobileMenuContainer>
      </HeaderContainer>
    </HeaderC>
  );
};

export default Header;

const ColoredLogo = styled(Logo)`
  ${({ theme }) => ({
    [theme.breakpoints.down("sm")]: {
      color: theme.palette.common.black,
    },
  })}
`;

const HeaderContainer = styled(Flex)`
  position: absolute;
  left: 0;
  align-items: center;
  top: 0;
  right: 0;
  z-index: 2;
  padding: 1rem 2rem;

  a {
    font-weight: 700;
  }

  ${({ theme }) => ({
    [theme.breakpoints.down("sm")]: {
      height: "60px",
      top: 0,
      marginTop: "-16px",
      zIndex: 3,
      position: "sticky",
      background: theme.palette.background.default,
    },
  })}
`;

const MobileMenuContainer = styled(Box)`
  position: absolute;
  right: 16px;
  top: 10px;
`;

const HeaderC = styled("header")`
  ${({ theme }) => ({
    [theme.breakpoints.down("sm")]: {
      width: "100%",
      height: "76px",
      top: "-16px",
      zIndex: 2,
      position: "sticky",
      "::before": {
        content: "''",
        top: "45px",
        boxShadow: "0px 2px 5px rgb(149 149 149 / 50%)",
      },
      "::after": {
        background: theme.palette.background.default,
        top: 0,
        zIndex: 2,
      },
      "::before,::after": {
        content: "''",
        display: "block",
        height: "16px",
        position: "sticky",
      },
    },
  })}
`;

import { Box, IconButton, styled, Typography } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import { Flex } from "./commonStyledComponents";
import { getContact } from "./contact/query";
import Link from "./Link";

const Footer = () => {
  const { data } = useQuery(["contact"], getContact);
  return (
    <FooterContainer>
      <IconButton
        component={Link}
        target="_blank"
        href={`https://www.instagram.com/${data?.data.attributes.instagram}`}
      >
        <img src="/instagram.svg" alt="Instagram"/>
      </IconButton>
      <IconButton
        component={Link}
        target="_blank"
        href={`https://www.facebook.com/${data?.data.attributes.instagram}`}
      >
        <img src="/facebook.svg" alt="FaceBook"/>
      </IconButton>
      <Flex alignItems="center" sx={{ mt: 1, ml: 1 }}>
        <Typography>© 2022 Firefly Studio</Typography>
        <Link href="/vseobecne-podminky-2023.pdf" target="_blank" sx={{ ml: 3 }}>
          Provozní řád studia
        </Link>
        <Link href="/ochrana-osobnich-udaju.pdf" target="_blank" sx={{ ml: 3 }}>
          Ochrana osobních údajů
        </Link>
      </Flex>
    </FooterContainer>
  );
};

const FooterContainer = styled(Box)`
  position: absolute;
  left: 0;
  width: 100%;
  bottom: 0;
  padding: 32px;

  ${({ theme }) => ({
    [theme.breakpoints.down("sm")]: {
      position: "relative",
    },
  })}
`;

export default Footer;

import { yupResolver } from "@hookform/resolvers/yup";
import { Button, useTheme } from "@mui/material";
import { Controller, useForm } from "react-hook-form";
import { Flex } from "../commonStyledComponents";
import { TextInput } from "../form/TextInput";
import { resetPasswordSchema } from "./validationSchema";

interface Props {
  onSubmit: (values: PasswordResetFormValues) => void;
}

export interface PasswordResetFormValues {
  password: string;
  passwordAgain: string;
}

export const PasswordResetForm = ({ onSubmit }: Props) => {
  const {
    handleSubmit,
    control,
    formState: { errors },
  } = useForm<PasswordResetFormValues>({
    resolver: yupResolver(resetPasswordSchema),
  });

  const { palette } = useTheme();

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Flex flexDirection="column" mt={4}>
        <Controller
          control={control}
          name="password"
          render={({ field }) => (
            <TextInput
              {...field}
              variant="outlined"
              placeholder="Nové heslo"
              type="password"
              sx={{ marginBottom: 2 }}
              helperText={errors.password?.message}
              error={!!errors.password?.message}
              FormHelperTextProps={{
                sx: {
                  color: `${palette.error.light} !important`,
                },
              }}
            />
          )}
        />
        <Controller
          control={control}
          name="passwordAgain"
          render={({ field }) => (
            <TextInput
              {...field}
              variant="outlined"
              placeholder="Znovu nové heslo"
              type="password"
              sx={{ marginBottom: 3 }}
              helperText={errors.passwordAgain?.message}
              error={!!errors.passwordAgain?.message}
              FormHelperTextProps={{
                sx: {
                  color: `${palette.error.light} !important`,
                },
              }}
            />
          )}
        />
        <Button type="submit" color="secondary" variant="contained">
          Uložit
        </Button>
      </Flex>
    </form>
  );
};

import * as yup from "yup";

export const resetPasswordSchema = yup
  .object()
  .shape({
    password: yup
      .string()
      .matches(
        /(^$|^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9]).{6,}$)/,
        "<PERSON><PERSON>lo musí obsahovat nejméně 6 znaků, jedno <PERSON>, jeden velký znak a jeden malý."
      ),
    passwordAgain: yup
      .string()
      .oneOf([yup.ref("password"), null], "<PERSON><PERSON><PERSON> se neshoduje"),
  })
  .required("Toto pole je požado<PERSON>é");

import { QueryFunctionContext } from "@tanstack/react-query";
import { axiosApi } from "../../axios";

export const isUserValidByToken = async ({
  queryKey,
}: QueryFunctionContext<[string, { token: string }]>): Promise<boolean> => {
  const [, { token }] = queryKey;
  const response = await axiosApi.get(`/auth/is-user-valid-by-token`, {
    params: {
      token,
    },
  });
  return response.data;
};

export const setNewPassword = async ({
  token,
  password,
}: {
  token: string;
  password: string;
}): Promise<boolean> => {
  const response = await axiosApi.put(`/auth/set-new-password`, {
    token,
    password,
  });
  return response.data;
};

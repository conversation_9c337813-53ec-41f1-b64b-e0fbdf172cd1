import { styled } from "@mui/material";
import ContentCopyIcon from "@mui/icons-material/ContentCopy";
import { IconButton } from "@mui/material";

interface Props {
  children: React.ReactNode;
  anchor: string;
}

export const ClipboardFeature = ({ children, anchor }: Props) => {
  return (
    <Wrapper>
      {children}
      <CopyButton
        aria-label="Copy link"
        size="small"
        onClick={() =>
          navigator.clipboard.writeText(
            `${window.location.origin}${window.location.pathname}#${anchor}`
          )
        }
      >
        <ContentCopyIcon fontSize="small" />
      </CopyButton>
    </Wrapper>
  );
};

const CopyButton = styled(IconButton)`
  position: absolute;
  flex-shrink: 0;
  left: -4px;
  top: 2px;

  svg {
    color: ${({ theme }) => theme.palette.text.primary};
  }
`;

const Wrapper = styled("span")`
  position: relative;
  margin: -24px;
  padding: 24px;

  button {
    display: none;
  }

  :hover {
    button {
      display: flex;
    }
  }
`;

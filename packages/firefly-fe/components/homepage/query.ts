import { axiosCms, StrapiResponse } from "../../axios";
import { HomepageResponse } from "./types";

export const getHomepage = async (): Promise<
  StrapiResponse<HomepageResponse>
> => {
  const response = await axiosCms.get("/homepage", {
    params: {
      "populate[0]": "lesson_types",
      "populate[1]": "activities.imagePreview",
      "populate[2]": "secondImage",
      "populate[3]": "thirdImage",
    },
  });
  return response.data;
};

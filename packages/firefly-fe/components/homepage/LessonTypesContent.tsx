import DoubleArrowIcon from "@mui/icons-material/DoubleArrow";
import { Box, Button, styled, Typography } from "@mui/material";
import Image from "next/image";
import { animated } from "react-spring";
import { getImageUrl } from "../../helpers/image";
import { scrollToElement } from "../../helpers/scroll";
import { routes } from "../../routes";
import AnimatingBlock from "../animatingBlock/AnimatingBlock";
import { Flex } from "../commonStyledComponents";
import Link from "../Link";
import { HomepageResponse } from "./types";

interface Props {
  data: HomepageResponse | undefined;
}

const CoursesContent = ({ data }: Props) => {
  return (
    <>
      <Flex
        alignItems="center"
        flexDirection="column"
        justifyContent="center"
        minWidth={{ sx: "auto", sm: 425 }}
        mx={{ xs: 5, sm: 20 }}
        mt={{ xs: 5, sm: 0 }}
      >
        <Typography variant="h1">{data?.secondTitle}</Typography>
        <Box sx={{ mt: 6, ml: { sx: 0, sm: 10 } }}>
          <Typography>{data?.secondDescription}</Typography>
          <Button
            variant="text"
            onClick={() => scrollToElement("lessonTypes")}
            sx={{
              mt: 5,
              fontSize: "1rem",
              fontWeight: 900,
              fontFamily: "HrotBasic",
              ml: {
                sm: "-80px",
              },
            }}
            endIcon={<DoubleArrowIcon />}
          >
            Typy lekcí
          </Button>
        </Box>
      </Flex>
      <Flex
        alignSelf="flex-end"
        position="relative"
        id="lessonTypes"
        flexDirection={{ xs: "column", sm: "row" }}
        height="100%"
        mt={{ xs: 5, sm: 0 }}
      >
        {data?.lesson_types?.data.map((l, index) => (
          <AnimatingBlock
            width={260}
            percentageHeight={45}
            animatedContentHeight={55}
            key={l.id}
            disableAnimationOnMobile
            index={index}
            render={({ zoomedContentStyle }) => (
              <Flex flexDirection="column" height="100%">
                <Box p={3}>
                  <Typography variant="h4" width={{ xs: "100%", sm: 215 }}>
                    {l?.attributes?.title}
                  </Typography>
                  <Typography mt={3} width={{ xs: "100%", sm: 215 }}>
                    {l?.attributes?.description}
                  </Typography>
                  {l?.attributes?.type !== "privateLesson" && (
                    <Link
                      sx={{ mt: 3, display: { xs: "flex", sm: "none" } }}
                      href={`${routes.booking}?lessonTypeIds=${l.id}`}
                      endIcon={<DoubleArrowIcon />}
                    >
                      Rozvrh
                    </Link>
                  )}
                  {l?.attributes?.type !== "privateLesson" && (
                    <Box sx={{ display: { xs: "none", sm: "block" } }}>
                      <animated.div style={zoomedContentStyle}>
                        <Link
                          sx={{ mt: 3 }}
                          href={`${routes.booking}?lessonTypeIds=${l.id}`}
                          endIcon={<DoubleArrowIcon />}
                        >
                          Rozvrh
                        </Link>
                      </animated.div>
                    </Box>
                  )}
                </Box>
              </Flex>
            )}
          />
        ))}
        {data?.secondImage.data.attributes.url && (
          <TopImageContainer>
            <Box position="relative" width="100%" height="100%">
              <Image
                layout="fill"
                objectFit="contain"
                alt={data?.secondTitle}
                src={getImageUrl(data.secondImage.data.attributes.url)}
              />
            </Box>
          </TopImageContainer>
        )}
      </Flex>
    </>
  );
};

export default CoursesContent;

const TopImageContainer = styled(Box)`
  position: absolute;
  top: 0;
  height: 60%;
  left: 10%;
  right: -4rem;
  z-index: -1;
`;

import { StrapiArrayResponse, StrapiResponse } from "../../axios";

export interface HomepageResponse {
  title: string;
  description: string;
  news: string;
  secondTitle: string;
  secondDescription: string;
  destinationDescription: string | null;
  thirdTitle: string;
  thirdDescription: string;
  secondImage: StrapiResponse<{
    alternativeText: string;
    url: string;
  }>;
  thirdImage: StrapiResponse<{
    alternativeText: string;
    url: string;
  }>;
  activities: StrapiArrayResponse<{
    title: string;
    description: string;
    news: string | null;
    shortDescription: string;
    slug: string;
    imagePreview: StrapiResponse<{
      alternativeText: string;
      url: string;
    }>;
  }>;
  lesson_types: StrapiArrayResponse<{
    title: string;
    description: string;
    type: "privateLesson" | "openSpace" | "course" | "openClass"
  }>;
}

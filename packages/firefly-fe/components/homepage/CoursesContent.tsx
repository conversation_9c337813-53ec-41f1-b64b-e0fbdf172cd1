import DoubleArrowIcon from "@mui/icons-material/DoubleArrow";
import { Box, Button, Typography } from "@mui/material";
import Image from "next/image";
import { animated } from "react-spring";
import { getImageUrl } from "../../helpers/image";
import { scrollToElement } from "../../helpers/scroll";
import { routes } from "../../routes";
import AnimatingBlock from "../animatingBlock/AnimatingBlock";
import { Flex } from "../commonStyledComponents";
import Link from "../Link";
import News from "../News";
import { HomepageResponse } from "./types";

interface Props {
  data: HomepageResponse | undefined;
}

const animationContentHeight = [155, 155, 145, 175];

const CoursesContent = ({ data }: Props) => {
  return (
    <>
      <Flex
        flexDirection="column"
        justifyContent="center"
        maxWidth={450}
        mx={{ xs: 3, sm: 20 }}
        flexShrink={0}
      >
        <Typography sx={{ mb: 2 }}>{data?.destinationDescription}</Typography>
        <Typography variant="h1">{data?.title}</Typography>
        <Box sx={{ mt: 6, ml: { sx: 0, sm: 10 } }}>
          <Typography maxWidth={400}>{data?.description}</Typography>
          <Button
            variant="text"
            onClick={() => scrollToElement("courses")}
            size="medium"
            sx={{
              mt: 5,
              fontSize: "1rem",
              fontWeight: 900,
              fontFamily: "HrotBasic",
              ml: {
                sm: "-80px",
              },
            }}
            endIcon={<DoubleArrowIcon />}
          >
            Naše kurzy
          </Button>
        </Box>
        <News sx={{ mt: 5 }} content={data?.news} />
      </Flex>

      <Flex
        alignSelf="flex-end"
        id="courses"
        flexDirection={{ xs: "column", sm: "row" }}
        mt={{ xs: 5, sm: 0 }}
        gap={{ xs: 4, sm: 0 }}
      >
        {data?.activities?.data.map((a, index) => (
          <AnimatingBlock
            width={260}
            percentageHeight={60}
            animatedContentHeight={animationContentHeight[index]}
            key={a.id}
            index={index}
            render={({ zoomedContentStyle }) => (
              <Flex
                flexDirection="column"
                justifyContent="space-between"
                height="100%"
              >
                <Box p={3}>
                  <Typography variant="h4" width={{ xs: "100%", sm: 320 }}>
                    {a?.attributes?.title}
                  </Typography>
                  <animated.div style={zoomedContentStyle}>
                    <Typography mt={3} width={{ xs: "100%", sm: 320 }}>
                      {a?.attributes?.shortDescription}
                    </Typography>
                    <Link
                      sx={{ mt: 3 }}
                      href={`${routes.activities}/${a.attributes.slug}`}
                      endIcon={<DoubleArrowIcon />}
                    >
                      Více
                    </Link>
                  </animated.div>
                </Box>
                <Box
                  position="relative"
                  width="100%"
                  height="100%"
                  minHeight={300}
                >
                  <Image
                    objectFit="cover"
                    layout="fill"
                    alt={a.attributes.title}
                    src={getImageUrl(
                      a.attributes.imagePreview.data.attributes.url
                    )}
                  />
                </Box>
              </Flex>
            )}
          />
        ))}
      </Flex>
    </>
  );
};

export default CoursesContent;

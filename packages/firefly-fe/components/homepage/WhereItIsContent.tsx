import { Box, styled, Typography } from "@mui/material";
import { getImageUrl } from "../../helpers/image";
import { Flex } from "../commonStyledComponents";
import { HomepageResponse } from "./types";

interface Props {
  data: HomepageResponse | undefined;
}

const WhereItIsContent = ({ data }: Props) => {
  return (
    <Flex
      height="100%"
      alignItems="center"
      flexDirection={{ xs: "column", sm: "row" }}
      mt={{ xs: 5, sm: 0 }}
    >
      <Flex
        alignItems="center"
        flexDirection="column"
        maxWidth={450}
        minWidth={{ sm: 300 }}
        mx={{ xs: 5, sm: 20 }}
      >
        <Typography variant="h1">{data?.thirdTitle}</Typography>
        <Box sx={{ mt: 6, ml: { sx: 0, sm: 10 } }}>
          <Typography>{data?.thirdDescription}</Typography>
        </Box>
      </Flex>
      <Img src={getImageUrl(data?.thirdImage?.data?.attributes?.url)} />
    </Flex>
  );
};

export default WhereItIsContent;

const Img = styled("img")`
  height: 100%;

  ${({ theme }) => ({
    [theme.breakpoints.down("md")]: {
      width: "100%",
      marginTop: "32px",
    },
  })}
`;

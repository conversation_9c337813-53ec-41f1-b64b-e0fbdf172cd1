import { BoxProps, css, Paper, styled, Typography } from "@mui/material";
import { useState } from "react";
import { animated, useSpring } from "react-spring";
import { useMeasure } from "react-use";
import { ShowMoreButton } from "./ShowMoreButton";

interface Props {
  content?: string | null;
  sx?: BoxProps["sx"];
}

const News = ({ content, sx }: Props) => {
  const [showMore, setShowMore] = useState(false);

  const [ref, { height }] = useMeasure<HTMLParagraphElement>();

  const expand = useSpring({
    height: showMore ? height : 20,
  });

  if (content == null) {
    return null;
  }

  return (
    <Paper
      sx={{
        ...sx,
        position: "relative",
        width: "100%",
        transition: "translate 2s",
      }}
    >
      <Typography variant="subtitle1" fontWeight={900} mb={1}>
        Novinky
      </Typography>
      <NewContainer style={expand}>
        <NewText ref={ref} more={showMore ? "true" : undefined}>
          {content}
        </NewText>
      </NewContainer>
      <div />
      <ShowMoreButton
        showMore={showMore}
        setShowMore={setShowMore}
        sx={{ position: "absolute", right: 12, top: 12 }}
      />
    </Paper>
  );
};

export default News;

const NewContainer = styled(animated.div)`
  overflow: hidden;
`;

const oneLineCss = css`
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
`;

const NewText = styled(Typography)<{ more?: string }>`
  margin: 0;
  overflow: auto;
  ${({ more }) => (more ? "" : oneLineCss)};
`;

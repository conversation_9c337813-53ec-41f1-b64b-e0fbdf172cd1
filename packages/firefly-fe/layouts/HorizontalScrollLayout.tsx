import { Box, styled } from "@mui/material";
import Head from "next/head";
import { useCallback, useEffect } from "react";
import { Flex } from "../components/commonStyledComponents";
import Footer from "../components/Footer";
import Header from "../components/header/Header";
import { useIsMobile } from "../helpers/device";
import { LinkPreview } from "../components/LinkPreview";
import { useRouter } from "next/router";

interface Props {
  children: React.ReactNode;
  title?: string;
  showFooter?: boolean;
  changeLinkColorOnDiv?: string;
  linkPreviewImage?: string;
  linkPreviewDescription?: string;
}

export const HorizontalScrollLayout = ({
  children,
  title,
  showFooter,
  changeLinkColorOnDiv,
  linkPreviewDescription,
  linkPreviewImage,
}: Props) => {
  const isMobile = useIsMobile();
  const router = useRouter();

  const scrollContentHorizontally = useCallback(
    (evt: WheelEvent) => {
      const scrollContainer = window.document.getElementById("content");
      if (scrollContainer && !isMobile) {
        const deltaX = Math.abs(evt.deltaX);
        const deltaY = Math.abs(evt.deltaY);
        scrollContainer.scrollLeft +=
          (deltaY > deltaX ? evt.deltaY : evt.deltaX) / 2;
        evt.preventDefault();
      }
    },
    [isMobile]
  );

  useEffect(() => {
    const scrollContainer = window.document.getElementById("content");
    scrollContainer?.addEventListener("wheel", scrollContentHorizontally);
    return () => {
      scrollContainer?.removeEventListener("wheel", scrollContentHorizontally);
    };
  }, [scrollContentHorizontally]);

  const fullTitle = `Firefly poledance - ${title}`;

  return (
    <Container>
      <Head>
        <title>{fullTitle}</title>
        <LinkPreview
          title={fullTitle}
          description={linkPreviewDescription}
          image={linkPreviewImage}
          siteUrl={`${process.env.NEXT_PUBLIC_APP_URL}${router.asPath}`}
        />
      </Head>
      <Header changeLinkColorOnDiv={changeLinkColorOnDiv} />
      <Content id="content">
        {children}
        {showFooter && <Footer />}
      </Content>
    </Container>
  );
};

const Container = styled(Box)`
  position: relative;
`;

const Content = styled(Flex)`
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  overflow-x: scroll;
  overflow-y: hidden;
  background-color: ${({ theme }) => theme.palette.background.default};

  ${({ theme }) => ({
    [theme.breakpoints.down("sm")]: {
      overflowX: "hidden",
      overflowY: "scroll",
      flexDirection: "column",
      position: "relative",
    },
  })}
`;

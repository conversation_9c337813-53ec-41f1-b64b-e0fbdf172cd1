import { Box, styled } from "@mui/material";
import Head from "next/head";
import { Flex } from "../components/commonStyledComponents";
import Footer from "../components/Footer";
import Header from "../components/header/Header";

interface Props {
  children: React.ReactNode;
  title?: string;
  showFooter?: boolean;
}

export const VerticalScrollLayout = ({
  children,
  title,
  showFooter,
}: Props) => {
  const fullTitle = `Firefly poledance - ${title}`;
  return (
    <Container>
      <Head>
        <title>{fullTitle}</title>
      </Head>
      <Header />
      <Content
        id="content"
        pt={{ xs: 3, sm: 20 }}
        px={{ xs: 1, sm: 2 }}
        width="100%"
      >
        {children}
      </Content>
      {showFooter && (
        <Box position="relative" height="150px" width="100%">
          <Footer />
        </Box>
      )}
    </Container>
  );
};

const Container = styled(Flex)`
  position: relative;
  flex-direction: column;
  background-color: ${({ theme }) => theme.palette.background.default};
  width: 100%;
  align-items: center;
  justify-content: center;
  min-height: 100%;
`;

const Content = styled(Box)`
  min-height: 100%;
  flex: 1;
  max-width: 1355px;
  margin: auto auto;
  padding-bottom: 64px;
`;

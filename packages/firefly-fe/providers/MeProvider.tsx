import {
  useMutation,
  UseMutationResult,
  useQuery
} from "@tanstack/react-query";
import { useRouter } from "next/router";
import React, { useContext } from "react";
import { useCookie } from "react-use";
import {
  getMe,
  LoginInput,
  LoginResponse,
  loginUser,
  RegisterInput,
  registerUser,
  ResetPasswordInput,
  resetUserPassword,
  UpdateInput,
  updateMe
} from "../components/user/query";
import { Customer } from "../reservanto/types";
import { useSnackbar } from "./SnackbarProvider";

interface MeContentType {
  me?: Customer;
  login?: UseMutationResult<LoginResponse, unknown, LoginInput, unknown>;
  logout: () => void;
  register?: UseMutationResult<void, unknown, RegisterInput, unknown>;
  resetPassword?: UseMutationResult<void, unknown, ResetPasswordInput, unknown>;
  updateMe?: UseMutationResult<Customer, unknown, UpdateInput, unknown>;
  meIsLoading: boolean;
  refetch: () => void
}

export const MeContext = React.createContext<MeContentType>({
  me: undefined,
  register: undefined,
  login: undefined,
  logout: () => {},
  resetPassword: undefined,
  updateMe: undefined,
  meIsLoading: false,
  refetch: () => {}
});

interface Props {
  children?: React.ReactNode;
}

export const REFRESH_TOKEN_KEY = "frt";
export const ACCESS_TOKEN_KEY = "fat";

const MeProvider = ({ children }: Props) => {
  const [, setRefreshToken, removeRefreshToken] = useCookie(REFRESH_TOKEN_KEY);
  const [, setAccessToken, removeAccessToken] = useCookie(ACCESS_TOKEN_KEY);
  const router = useRouter();

  const { showSnackbar } = useSnackbar();

  const loginMutation = useMutation(loginUser, {
    onSuccess: (data) => {
      setRefreshToken(data.refreshToken);
      setAccessToken(data.accessToken);
      showSnackbar({
        severity: "success",
        message: "Vítej u nás!",
      });
      refetch();
    },
    onError: (data: any) => {
      showSnackbar({
        severity: "error",
        message: data.response.data,
      });
    },
  });
  const registerMutation = useMutation(registerUser, {
    onSuccess: () => {
      showSnackbar({
        severity: "success",
        message: "Účet vytvořen, nyní se můžeš přihlásit",
      });
    },
    onError: (data: any) => {
      showSnackbar({
        severity: "error",
        message: data.response.data,
      });
    },
  });
  const resetPasswordMutation = useMutation(resetUserPassword, {
    onSuccess: () => {
      showSnackbar({
        severity: "success",
        message: "Byl ti zaslán e-mail s instrukcemi na resetovaní hesla.",
      });
    },
  });
  const updateMeMutation = useMutation(updateMe, {
    onSuccess: () => {
      refetch();
      showSnackbar({
        severity: "success",
        message: "Tvé údaje byly aktualizovány",
      });
    },
    onError: (data: any) => {
      showSnackbar({
        severity: "error",
        message: data.response.data,
      });
    },
  });

  const { data, refetch, isLoading } = useQuery(["me"], getMe, {
    keepPreviousData: true,
  });

  const handleLogout = async () => {
    removeRefreshToken();
    removeAccessToken();

    if (router.pathname.includes("profil")) {
      router.push("/", undefined, {
        shallow: true,
      });
    }

    await refetch();
    showSnackbar({
      severity: "success",
      message: "Odhlášení proběhlo úspěšně",
    });
  };

  const context = {
    me: data,
    refetch, 
    meIsLoading: isLoading,
    login: loginMutation,
    register: registerMutation,
    resetPassword: resetPasswordMutation,
    logout: handleLogout,
    updateMe: updateMeMutation,
  };

  return <MeContext.Provider value={context}>{children}</MeContext.Provider>;
};
export default MeProvider;

export const useMe = () => {
  const context = useContext(MeContext);
  return context;
};

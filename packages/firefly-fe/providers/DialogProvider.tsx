import { useRouter } from "next/router";
import React, { useContext, useEffect, useState } from "react";
import { BookingDialogContent } from "../components/booking";
import { BookingDoneDialogContent } from "../components/booking/dialog/BookingDoneDialogContent";
import { BookingInfoDialogContent } from "../components/booking/dialog/BookingInfoDialogContent";
import { bankInfo } from "../components/booking/payment/helpers";
import { PaymentDialogContent } from "../components/booking/payment/PaymentDialogContent";
import { CenterDialog } from "../components/dialog/CenterDialog";
import { RightSwipingDialog } from "../components/dialog/RightSwipingDialog";
import { ConfirmCancelDialog } from "../components/profile/reservations/ConfirmCancelDialog";
import { LoginDialogContent } from "../components/user/LoginDialogContent";
import { RegistrationDialogContent } from "../components/user/RegistrationDialogContent";
import { ResetPasswordDialogContent } from "../components/user/ResetPasswordDialogContent";
import { routes } from "../routes";

type DialogType =
  | "booking"
  | "login"
  | "registration"
  | "resetPassword"
  | "bookingInfo"
  | "bookingDone"
  | "confirmCancel"
  | "payment";

interface DialogState extends Options {
  isOpen: boolean;
  slides: DialogType[];
  type: "center" | "right" | null;
}

interface Options {
  appointmentId?: number | null;
  bookingServiceId?: number;
  courseId?: number | null;
  onCancel?: () => void,
  onConfirm?: () => void,
  payment?: {
    type: keyof typeof bankInfo;
    amount?: number;
  };
}

interface DialogContentType {
  dialog: DialogState;
  goBack: () => void;
  goTo: (
    name: DialogType,
    options?: Options,
    type?: "center" | "right"
  ) => void;
  close: (option?: { keepParams?: boolean }) => void;
}

export const DialogContext = React.createContext<DialogContentType>({
  goBack: () => {},
  goTo: () => {},
  close: () => {},
  dialog: {
    isOpen: false,
    appointmentId: null,
    slides: [],
    type: null,
    onCancel: () => {},
    onConfirm: () => {}
  },
});

interface Props {
  children?: React.ReactNode;
}

const DialogProvider = ({ children }: Props) => {
  const router = useRouter();
  const [dialog, setDialog] = useState<DialogState>({
    isOpen: false,
    slides: [],
    appointmentId: null,
    type: null,
  });

  const handleClose = (options?: { keepParams?: boolean }) => {
    setDialog({
      isOpen: false,
      slides: [],
      appointmentId: null,
      type: null,
    });

    if (router.query.appointmentId && !options?.keepParams) {
      router.push(
        {
          pathname: router.pathname,
          query: {
            ...router.query,
            appointmentId: [],
          },
        },
        undefined,
        { shallow: true }
      );
    }
  };

  useEffect(() => {
    if (
      router.query.appointmentId &&
      !dialog.appointmentId &&
      !router.pathname.includes(routes.profile)
    ) {
      goTo(
        "booking",
        { appointmentId: Number(router.query.appointmentId) },
        "right"
      );
    }
  }, [router.query.appointmentId]);

  const goBack = () => {
    setDialog((prevState) => ({
      ...prevState,
      slides: prevState.slides.slice(0, -1),
    }));
  };

  const goTo = (
    stepName: DialogType,
    options?: Options,
    type?: "center" | "right"
  ) => {
    setDialog((prevState) => ({
      appointmentId: options?.appointmentId || prevState.appointmentId || null,
      ...options,
      isOpen: true,
      slides: [...prevState.slides, stepName],
      type: type || "right",
    }));
    if (options?.appointmentId && stepName !== "payment")
      router.push(
        {
          pathname: router.pathname,
          query: {
            ...router.query,
            appointmentId: options.appointmentId,
          },
        },
        undefined,
        {
          shallow: true,
        }
      );
  };

  const recentSlideType = dialog.slides[dialog.slides.length - 1];

  const getRecentDialog = () => {
    switch (recentSlideType) {
      case "registration":
        return {
          title: "Zaregistuj se",
          comp: <RegistrationDialogContent />,
        };
      case "login":
        return {
          title: "Přihlaš se",
          comp: <LoginDialogContent />,
        };
      case "booking":
        return {
          comp: (
            <BookingDialogContent
              appointmentId={dialog.appointmentId || null}
            />
          ),
        };
      case "bookingInfo":
        return {
          comp: (
            <BookingInfoDialogContent
              appointmentId={dialog.appointmentId || null}
            />
          ),
        };
      case "confirmCancel":
        return {
          comp: (
            <ConfirmCancelDialog />
          )
        }
      case "bookingDone":
        return {
          comp: (
            <BookingDoneDialogContent
              appointmentId={dialog.appointmentId || null}
            />
          ),
        };
      case "resetPassword":
        return {
          title: "Zapomenuté heslo",
          comp: <ResetPasswordDialogContent />,
        };
      case "payment":
        return {
          comp: <PaymentDialogContent />,
        };
      default:
        return null;
    }
  };

  const context = {
    dialog,
    goBack,
    goTo,
    close: handleClose,
  };
  const recentDialog = getRecentDialog();
  return (
    <DialogContext.Provider value={context}>
      {children}
      {dialog.type === "right" ? (
        <RightSwipingDialog
          title={recentDialog?.title}
          hideBack={recentSlideType.includes("booking")}
        >
          {" "}
          {recentDialog?.comp}
        </RightSwipingDialog>
      ) : (
        <CenterDialog title={recentDialog?.title}>
          {" "}
          {recentDialog?.comp}
        </CenterDialog>
      )}
    </DialogContext.Provider>
  );
};
export default DialogProvider;

export const useDialog = () => {
  const context = useContext(DialogContext);
  return context;
};

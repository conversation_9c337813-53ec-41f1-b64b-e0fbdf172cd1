import { Box, LinearProgress, styled } from "@mui/material";
import { useRouter } from "next/router";
import React, { useContext, useEffect, useState } from "react";
import { scrollToElement } from "../helpers/scroll";

interface LoadingContentType {
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  isLoading: boolean;
}

export const LoadingContext = React.createContext<LoadingContentType>({
  setIsLoading: () => {},
  isLoading: false,
});

interface Props {
  children?: React.ReactNode;
}
const LoadingProvider = ({ children }: Props) => {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  useEffect(() => {
    if (window.location.hash) {
      setTimeout(
        () => scrollToElement(window.location.hash.replace("#", "")),
        0
      );
    }
  }, [typeof window !== "undefined" && window.location]);

  useEffect(() => {
    const handleRouteChange = (url, { shallow }) => {
      const firstPartNewUrl = url.split("/")?.[1];
      const firstPartOldUrl = router.pathname.split("/")?.[1];
      if (!shallow || firstPartNewUrl !== firstPartOldUrl) {
        setIsLoading(true);
      }
    };

    const handleChangeComplete = () => {
      setIsLoading(false);
    };

    router.events.on("routeChangeStart", handleRouteChange);
    router.events.on("routeChangeComplete", handleChangeComplete);

    return () => {
      router.events.off("routeChangeStart", handleRouteChange);
      router.events.on("routeChangeComplete", handleChangeComplete);
    };
  }, []);

  const context = {
    isLoading,
    setIsLoading,
  };
  return (
    <LoadingContext.Provider value={context}>
      {children}
      {isLoading && (
        <FixedContainer>
          <LinearProgress color="secondary" sx={{ height: "0.5rem" }} />
        </FixedContainer>
      )}
    </LoadingContext.Provider>
  );
};
export default LoadingProvider;

export const useLoading = () => {
  const context = useContext(LoadingContext);
  return context;
};

const FixedContainer = styled(Box)`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
`;

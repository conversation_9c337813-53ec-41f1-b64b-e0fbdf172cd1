import { Alert, AlertColor, Snackbar } from "@mui/material";
import { styled } from "@mui/system";
import React, { useContext, useState } from "react";

interface SnackbarContentType {
  showSnackbar: (snackbar: SnackbarState) => void;
}

export const SnackbarContent = React.createContext<SnackbarContentType>({
  showSnackbar: () => {},
});

interface Props {
  children?: React.ReactNode;
}

interface SnackbarState {
  severity: AlertColor | null;
  message: string | null;
  isOpen?: boolean;
}

const SnackbarProvider = ({ children }: Props) => {
  const [snackBar, setSnackBar] = useState<SnackbarState>({
    isOpen: false,
    message: null,
    severity: null,
  });

  const handleClose = () => {
    setSnackBar({
      isOpen: false,
      message: null,
      severity: null,
    });
  };

  const showSnackbar = ({ message, severity }: SnackbarState) => {
    setSnackBar({
      isOpen: true,
      message,
      severity,
    });
  };

  const context = {
    showSnackbar,
  };

  return (
    <SnackbarContent.Provider value={context}>
      {children}
      <Snackbar
        onClose={handleClose}
        open={snackBar.isOpen}
        autoHideDuration={6000}
        key={snackBar.message}
        anchorOrigin={{ vertical: "top", horizontal: "center" }}
      >
        <CustomAlert
          onClose={handleClose}
          severity={snackBar.severity || undefined}
          sx={{ width: "100%", fontSize: "1.2rem" }}
        >
          {snackBar.message}
        </CustomAlert>
      </Snackbar>
    </SnackbarContent.Provider>
  );
};
export default SnackbarProvider;

export const useSnackbar = () => {
  const context = useContext(SnackbarContent);
  return context;
};

const CustomAlert = styled(Alert)`
  width: 100%;
  font-size: 1.2rem;
  box-shadow: 0px 2px 1px -1px rgb(0 0 0 / 20%),
    0px 1px 1px 0px rgb(0 0 0 / 14%), 0px 1px 3px 0px rgb(0 0 0 / 12%);

  svg {
    width: 1.2rem;
    height: 1.5rem;
    padding-top: 0.25rem;
  }
`;

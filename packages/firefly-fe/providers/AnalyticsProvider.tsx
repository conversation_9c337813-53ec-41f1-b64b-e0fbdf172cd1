import { useRouter } from "next/router";
import React, { useContext, useEffect } from "react";
import { useLocalStorage } from "../libs/hooks";
import { CookiesBanner } from "../components/CookiesBanner";
import { FBpageview, initGA, logPageView } from "../libs/analytics";
import Script from "next/script";

interface Cookies {
  allowed: boolean;
  hideUntil: number;
}

interface AnalyticsProviderType {
  allowCookies: () => void;
  disableCookies: () => void;
  cookies?: Cookies;
}

export const AnalyticsContext = React.createContext<AnalyticsProviderType>({
  allowCookies: () => {},
  cookies: { hideUntil: 0, allowed: false },
  disableCookies: () => {},
});

interface Props {
  children?: React.ReactNode;
}

const AnalyticsProvider = ({ children }: Props) => {
  const router = useRouter();
  const [cookies, setCookiesAgreement] = useLocalStorage<Cookies>(
    "cookiesAgreementFirefly",
    {
      hideUntil: 1662034331201,
      allowed: false,
    }
  );

  useEffect(() => {
    initGA();
    // `routeChangeComplete` won't run for the first page load unless the query string is
    // hydrated later on, so here we log a page view if this is the first render and
    // there's no query string
    if (!router.asPath.includes("?")) {
      logPageView();
      FBpageview();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    (window as any)[
      `ga-disable-${process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID}`
    ] = !cookies?.allowed;
  }, [cookies?.allowed]);

  useEffect(() => {
    router.events.on("routeChangeComplete", logPageView);
    return () => {
      router.events.off("routeChangeComplete", logPageView);
    };
  }, [router.events]);

  const disableCookies = () => {
    const now = new Date().getTime();
    // show cookiesBanner tomorrow again
    setCookiesAgreement({
      allowed: false,
      hideUntil: now + 86400000,
    });
  };

  const allowCookies = () => {
    const now = new Date().getTime();
    setCookiesAgreement({
      hideUntil: now + 31556926000,
      allowed: true,
    });
  };

  return (
    <AnalyticsContext.Provider
      value={{
        allowCookies,
        cookies,
        disableCookies,
      }}
    >
      <Script
        id="fb-pixel"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            !function(f,b,e,v,n,t,s)
            {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
            n.callMethod.apply(n,arguments):n.queue.push(arguments)};
            if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
            n.queue=[];t=b.createElement(e);t.async=!0;
            t.src=v;s=b.getElementsByTagName(e)[0];
            s.parentNode.insertBefore(t,s)}(window, document,'script',
            'https://connect.facebook.net/en_US/fbevents.js');
            fbq('init', ${process.env.NEXT_PUBLIC_FACEBOOK_PIXEL_ID});
          `,
        }}
      />
      <CookiesBanner />
      {children}
    </AnalyticsContext.Provider>
  );
};
export default AnalyticsProvider;

export const useAnalytics = () => {
  const context = useContext(AnalyticsContext);
  return context;
};

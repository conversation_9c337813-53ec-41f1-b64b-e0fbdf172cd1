import ReactGA from "react-ga4";

export const initGA = () => {
  if (process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID) {
    console.log("GA init");

    ReactGA.initialize(process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID);
  }
};

export const logPageView = () => {
  FBpageview();
  ReactGA.set({ page: window.location.pathname });
  ReactGA.send({
    hitType: "pageview",
    page: window.location.pathname + window.location.search,
  });
};

export const FBpageview = () => {
  (window as any).fbq("track", "PageView");
};

// https://developers.facebook.com/docs/facebook-pixel/advanced/
export const FBevent = (name: string, options = {}) => {
  (window as any).fbq("track", name, options);
};

import type { NextPage } from "next";
import { useMutation, useQuery } from "@tanstack/react-query";
import {
  PasswordResetForm,
  PasswordResetFormValues,
} from "../components/passwordReset/PasswordResetForm";
import { VerticalScrollLayout } from "../layouts/VerticalScrollLayout";
import { Box, CircularProgress, Typography } from "@mui/material";
import { useRouter } from "next/router";
import {
  isUserValidByToken,
  setNewPassword,
} from "../components/passwordReset/query";
import { useSnackbar } from "../providers/SnackbarProvider";
import { useDialog } from "../providers/DialogProvider";

const PasswordReset: NextPage = () => {
  const {
    query: { token },
  } = useRouter();
  const { push } = useRouter();
  const { showSnackbar } = useSnackbar();
  const { goTo } = useDialog();

  const { data, isLoading, isError } = useQuery(
    ["userByToken", { token: token as string }],
    isUserValidByToken,
    {
      enabled: !!token,
    }
  );

  const setNewPasswordMutation = useMutation(
    ["setNewPassword"],
    setNewPassword
  );

  const handleSubmit = (values: PasswordResetFormValues) => {
    setNewPasswordMutation.mutate(
      {
        token: token as string,
        password: values.password,
      },
      {
        onSuccess: () => {
          showSnackbar({
            severity: "success",
            message: "Nové heslo nastaveno, nyní se můžete přihlásit.",
          });
          push("/");
          goTo("login");
        },
      }
    );
  };

  return (
    <VerticalScrollLayout title="Resetování hesla" showFooter>
      <Box margin="auto auto" maxWidth={720}>
        {isError ? (
          <Typography variant="h4">Neplatný odkaz.</Typography>
        ) : isLoading ? (
          <CircularProgress />
        ) : (
          <>
            <Typography variant="h4">Nastavte si nové heslo.</Typography>
            <PasswordResetForm onSubmit={handleSubmit} />
          </>
        )}
      </Box>
    </VerticalScrollLayout>
  );
};

export default PasswordReset;

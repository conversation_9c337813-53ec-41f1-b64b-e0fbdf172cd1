import { dehydrate, QueryClient, useQuery } from "@tanstack/react-query";
import { GetServerSideProps, NextPage } from "next";
import { useRouter } from "next/router";
import { LectorInfo } from "../../components/lectorDetail/LectorInfo";
import { PhotoWithName } from "../../components/lectorDetail/PhotoWithName";
import { getLector } from "../../components/lectorDetail/query";
import { HorizontalScrollLayout } from "../../layouts/HorizontalScrollLayout";
import { prefetchHeader } from "../../ssr/prefetch";

interface Props {
  prevUrl?: string;
}

const LectorDetailPage: NextPage = ({ prevUrl }: Props) => {
  const { query } = useRouter();
  const { data } = useQuery(
    ["lector", { slug: query.slug as string }],
    getLector
  );
  const lector = data?.data[0];

  return (
    <HorizontalScrollLayout
      title={lector?.attributes.name}
      changeLinkColorOnDiv="lectorImage"
    >
      <PhotoWithName data={lector?.attributes} prevUrl={prevUrl} />
      <LectorInfo data={lector?.attributes} />
    </HorizontalScrollLayout>
  );
};

export default LectorDetailPage;

export const getServerSideProps: GetServerSideProps = async ({
  query,
  req,
}) => {
  const queryClient = new QueryClient();

  await Promise.all([
    prefetchHeader(queryClient),
    queryClient.prefetchQuery(
      ["lector", { slug: query.slug as string }],
      getLector
    ),
  ]);

  return {
    props: {
      dehydratedState: dehydrate(queryClient),
      prevUrl: req.headers.referer || null,
    },
  };
};

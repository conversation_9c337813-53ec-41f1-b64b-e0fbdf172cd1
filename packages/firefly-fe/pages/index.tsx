import { dehydrate, QueryClient, useQuery } from "@tanstack/react-query";
import type { NextPage } from "next";
import {
  CoursesContent,
  LessonTypesContent,
  WhereItIsContent,
} from "../components/homepage";
import { getHomepage } from "../components/homepage/query";
import { HorizontalScrollLayout } from "../layouts/HorizontalScrollLayout";
import { prefetchHeader } from "../ssr/prefetch";

const Home: NextPage = () => {
  const { data } = useQuery(["homepage"], getHomepage);

  return (
    <HorizontalScrollLayout
      title={data?.data.attributes.title}
      showFooter
      linkPreviewImage={data?.data.attributes.secondImage.data.attributes.url}
      linkPreviewDescription={data?.data.attributes.description}
    >
      <CoursesContent data={data?.data?.attributes} />
      <LessonTypesContent data={data?.data?.attributes} />
      <WhereItIsContent data={data?.data?.attributes} />
    </HorizontalScrollLayout>
  );
};

export default Home;

export const getStaticProps = async () => {
  const queryClient = new QueryClient();
  await Promise.all([
    prefetchHeader(queryClient),
    queryClient.prefetchQuery(["homepage"], getHomepage),
  ]);
  return {
    props: {
      dehydratedState: dehydrate(queryClient),
    },
  };
};

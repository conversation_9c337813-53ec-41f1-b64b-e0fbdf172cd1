import { ThemeProvider } from "@emotion/react";
import {
  Hydrate,
  QueryClient,
  QueryClientProvider,
} from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import type { AppProps } from "next/app";
import Head from "next/head";
import { useState } from "react";
import DialogProvider from "../providers/DialogProvider";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { LocalizationProvider } from "@mui/x-date-pickers";

import "../styles/globals.css";
import "mapbox-gl/dist/mapbox-gl.css";
import theme from "../theme";
import LoadingProvider from "../providers/LoadingProvider";
import SnackbarProvider from "../providers/SnackbarProvider";
import UserProvider from "../providers/MeProvider";
import AnalyticsProvider from "../providers/AnalyticsProvider";

function MyApp({ Component, pageProps }: AppProps) {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            refetchOnWindowFocus: false,
            retry: 0
          },
        },
      })
  );

  return (
    <QueryClientProvider client={queryClient}>
      <Head>
        <link
          rel="stylesheet"
          href="https://fonts.googleapis.com/icon?family=Material+Icons"
        />
        <meta name="viewport" content="initial-scale=1, width=device-width" />
      </Head>
      <ThemeProvider theme={theme}>
        <Hydrate state={pageProps.dehydratedState}>
          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <LoadingProvider>
              <SnackbarProvider>
                <UserProvider>
                  <DialogProvider>
                    <AnalyticsProvider>
                      <Component {...pageProps} />
                    </AnalyticsProvider>
                  </DialogProvider>
                </UserProvider>
              </SnackbarProvider>
            </LoadingProvider>
          </LocalizationProvider>
        </Hydrate>
      </ThemeProvider>
      <ReactQueryDevtools />
    </QueryClientProvider>
  );
}

export default MyApp;

import { dehydrate, QueryClient, useQuery } from "@tanstack/react-query";
import type { NextPage } from "next";
import { MainInfo } from "../components/contact/MainInfo";
import { getContact } from "../components/contact/query";
import { HorizontalScrollLayout } from "../layouts/HorizontalScrollLayout";
import { Locations } from "../components/contact/Locations";
import { prefetchHeader } from "../ssr/prefetch";

const Contact: NextPage = () => {
  const { data } = useQuery(["contact"], getContact);

  return (
    <HorizontalScrollLayout title="Kontakt" showFooter>
      <MainInfo data={data?.data.attributes} />
      <Locations data={data?.data.attributes} />
    </HorizontalScrollLayout>
  );
};

export default Contact;

export const getStaticProps = async () => {
  const queryClient = new QueryClient();
  await Promise.all([
    prefetchHeader(queryClient),
    queryClient.prefetchQuery(["contact"], getContact),
  ]);
  return {
    props: {
      dehydratedState: dehydrate(queryClient),
    },
  };
};

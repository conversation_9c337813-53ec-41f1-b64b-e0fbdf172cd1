import { dehydrate, QueryClient } from "@tanstack/react-query";
import type { NextPage } from "next";
import { getContact } from "../components/contact/query";
import { HorizontalScrollLayout } from "../layouts/HorizontalScrollLayout";
import { useQuery } from "@tanstack/react-query";
import { Locations } from "../components/contact/Locations";
import { prefetchHeader } from "../ssr/prefetch";
import { MainInfo } from "../components/englishInfo/MainInfo";

const Info: NextPage = () => {
  const { data } = useQuery(["contact"], getContact);
  return (
    <HorizontalScrollLayout title="Info" showFooter>
      <MainInfo data={data?.data.attributes} />
      <Locations data={data?.data.attributes} />
    </HorizontalScrollLayout>
  );
};

export default Info;

export const getStaticProps = async () => {
  const queryClient = new QueryClient();
  await Promise.all([
    prefetchHeader(queryClient),
    queryClient.prefetchQuery(["contact"], getContact),
  ]);
  return {
    props: {
      dehydratedState: dehydrate(queryClient),
    },
  };
};

import { Box, Button } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import type { NextPage } from "next";
import { Flex } from "../components/commonStyledComponents";
import { Credit } from "../components/profile/credit/Credit";
import { ProfileInfo } from "../components/profile/ProfileInfo";
import { Replacements } from "../components/profile/replacements/Replacements";
import { getReservations } from "../components/profile/reservations/query";
import { Reservations } from "../components/profile/reservations/Reservations";
import { SeasonTicket } from "../components/profile/seasonTicket/SeasonTicket";
import { Videos } from "../components/profile/videos/Videos";
import { VerticalScrollLayout } from "../layouts/VerticalScrollLayout";
import { useMe } from "../providers/MeProvider";
import { Event } from "../reservanto/types";

const Contact: NextPage = () => {
  const { logout, me } = useMe();

  const { data: reservations, isLoading } = useQuery(
    ["reservations"],
    getReservations,
    { enabled: !!me?.Id }
  );

  const handleLogout = () => {
    logout();
  };

  const videoBookingServiceIds = reservations
    ?.filter((r) => !!(r as Event).IsPaid)
    .map((r) => r.BookingServiceId);

  return (
    <VerticalScrollLayout title="Profil" showFooter>
      <Flex justifyContent="space-between">
        <ProfileInfo data={me} />
        <Button
          variant="outlined"
          color="primary"
          onClick={handleLogout}
          sx={{
            alignSelf: "flex-start",
            display: { xs: "none", sm: "inherit" },
          }}
          size="medium"
        >
          Odhlásit
        </Button>
      </Flex>
      <Box>
        <Reservations reservations={reservations} isLoading={isLoading} />
        <Credit data={me} />
        <Replacements data={me} />
        <SeasonTicket data={me} />
        <Videos bookingServiceIds={videoBookingServiceIds} />
      </Box>
    </VerticalScrollLayout>
  );
};

export default Contact;

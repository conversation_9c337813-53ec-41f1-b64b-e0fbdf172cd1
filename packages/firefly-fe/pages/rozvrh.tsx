import { Box, Typography } from "@mui/material";
import { dehydrate, QueryClient } from "@tanstack/react-query";
import type { GetServerSideProps, NextPage } from "next";
import { Booking } from "../components/booking/Booking";
import { VerticalScrollLayout } from "../layouts/VerticalScrollLayout";
import { prefetchHeader } from "../ssr/prefetch";

const Home: NextPage = () => {
  return (
    <VerticalScrollLayout title="Rezervace">
      <Typography variant="h1">Rozvrh</Typography>
      <Box mt={7}>
        <Booking />
      </Box>
    </VerticalScrollLayout>
  );
};

export default Home;

export const getServerSideProps: GetServerSideProps = async () => {
  const queryClient = new QueryClient();

  await Promise.all([prefetchHeader(queryClient)]);
  return {
    props: {
      dehydratedState: dehydrate(queryClient),
    },
  };
};

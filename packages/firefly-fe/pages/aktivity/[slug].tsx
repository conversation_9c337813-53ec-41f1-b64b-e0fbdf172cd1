import { dehydrate, QueryClient, useQuery } from "@tanstack/react-query";
import { GetStaticProps, NextPage } from "next";
import { useRouter } from "next/router";
import { IntroContent } from "../../components/activityDetail";
import {
  getActivity,
  getPromotedLesson,
} from "../../components/activityDetail/query";
import TryItContent from "../../components/activityDetail/TryItContent";
import { HorizontalScrollLayout } from "../../layouts/HorizontalScrollLayout";
import { getAllActivitySlugs } from "../../helpers/getAllActivitySlugs";
import { prefetchHeader } from "../../ssr/prefetch";

const CourseDetailPage: NextPage = () => {
  const { query } = useRouter();

  const { data } = useQuery(
    ["activity", { slug: query.slug! as string }],
    getActivity
  );
  const activity = data?.data[0];

  return (
    <HorizontalScrollLayout
      title={activity?.attributes.title}
      linkPreviewDescription={activity?.attributes.description}
      linkPreviewImage={activity?.attributes.image.data.attributes.url}
    >
      <IntroContent data={activity?.attributes} activityId={activity?.id!} />
      <TryItContent data={activity?.attributes} activityId={activity?.id!} />
    </HorizontalScrollLayout>
  );
};

export default CourseDetailPage;

export async function getStaticPaths() {
  return {
    paths: getAllActivitySlugs(),
    fallback: false,
  };
}

export const getStaticProps: GetStaticProps = async ({ params }) => {
  const queryClient = new QueryClient();

  const slugArray = (params!.slug as string).split("-");
  const activityId = slugArray[slugArray.length - 1];

  await Promise.all([
    prefetchHeader(queryClient),
    queryClient.prefetchQuery(
      ["activity", { slug: params!.slug as string }],
      getActivity
    ),
    queryClient.prefetchQuery(
      ["promotedLesson", { activityId: Number(activityId) }],
      getPromotedLesson
    ),
  ]);

  return {
    props: {
      dehydratedState: dehydrate(queryClient),
    },
    revalidate: 60,
  };
};

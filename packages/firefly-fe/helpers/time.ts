import moment from "moment-timezone";

export const padTo2Digits = (num: number) => {
  return num.toString().padStart(2, "0");
};

interface Option {
  year: boolean;
}
export const getFormattedDate = (date: Date, option?: Option) => {
  const formattedDate = [date.getDate(), date.getMonth() + 1];
  if (option?.year) {
    formattedDate.push(date.getFullYear());
  }

  return formattedDate.join(". ") + (option?.year ? "" : ".");
};

export const getFormattedTime = (date: Date) => {
  const pragueDate = moment.tz(date, 'Europe/Prague');
  return [pragueDate.hours(), padTo2Digits(pragueDate.minutes())].join(":");
};

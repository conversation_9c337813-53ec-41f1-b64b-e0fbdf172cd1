import { useEffect, useRef, useState } from "react";

interface Props {
  onDivId?: string;
  fromColor: string;
  toColor: string;
}

export const useChangeColorOn = ({ onDivId, toColor, fromColor }: Props) => {
  const itemRef = useRef<any>(null);
  const [color, setColor] = useState(fromColor);

  useEffect(() => {
    if (onDivId) {
      const scrollContent = document.getElementById("content");
      const backgroundElement = document.getElementById(onDivId);
      if (backgroundElement && scrollContent) {
        const onScroll = () => {
          setColor(
            (itemRef.current?.offsetLeft || 0) >
              backgroundElement.offsetWidth - scrollContent.scrollLeft
              ? fromColor
              : toColor
          );
        };

        setTimeout(() => onScroll(), 500);
        window.addEventListener("wheel", onScroll, {});
        window.addEventListener("resize", onScroll);
        return () => {
          window.removeEventListener("wheel", onScroll);
          window.addEventListener("resize", onScroll);
        };
      }
    }
  }, [onDivId, fromColor, toColor]);

  return { itemRef, color };
};

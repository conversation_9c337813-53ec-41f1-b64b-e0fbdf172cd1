export const scrollToElement = (id: string) => {
  const element = document.getElementById(id);
  const scrollableContent = document.getElementById("content");
  const isMobile = window.innerWidth < 600;
  if (element) {
    if (isMobile) {
      window?.scrollTo({
        top: element.offsetTop,
        behavior: "smooth",
      });
    } else {
      scrollableContent?.scrollTo({
        left: element.offsetLeft - 64,
        behavior: "smooth",
      });
    }
  }
};

export const resetScroll = () => {
  const scrollableContent = document.getElementById("content");
  const isMobile = window.innerWidth < 600;
  if (isMobile) {
    window?.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  } else {
    scrollableContent?.scrollTo({
      left: 0,
      behavior: "smooth",
    });
  }
};

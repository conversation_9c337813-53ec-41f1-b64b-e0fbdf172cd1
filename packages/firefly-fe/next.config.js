/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,

  webpack(config) {
    const fileLoaderRule = config.module.rules.find(
      (rule) => rule.test && rule.test.test(".svg")
    );
    fileLoaderRule.exclude = /\.svg$/;
    config.module.rules.push({
      test: /\.svg$/,
      loader: require.resolve("@svgr/webpack"),
    });
    return config;
  },
  images: {
    domains: ["localhost", "firefly-gamma.vercel.app", "res.cloudinary.com"],
  },
  output: "standalone",
  async rewrites() {
    return [
      {
        source: "/cms/:path*",
        destination: `${process.env.NEXT_PUBLIC_STRAPI_URL}/api/:path*`,
      },
      {
        source: "/api/:path*",
        destination: `${process.env.NEXT_PUBLIC_API_URL}/:path*`,
      },
    ];
  },
};

module.exports = nextConfig;

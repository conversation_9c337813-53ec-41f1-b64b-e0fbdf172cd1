import { StrapiArrayResponse, StrapiResponse } from "../axios";

export interface Activity {
  id: string;
  title: string;
  description: string;
  tryItText: string;
  news: string;
  image: StrapiResponse<{
    alternativeText: string;
    url: string;
  }>;
  slug: string;
  lesson_types: StrapiArrayResponse<{
    title: string;
    description: string;
    type: "privateLesson" | "openSpace" | "course" | "openClass";
  }>;
  lectors: StrapiArrayResponse<{
    name: string;
    email: string;
    phoneNumber: string;
    image: StrapiResponse<{
      alternativeText: string;
      url: string;
    }>;
  }>;
}

export interface LessonType {
  title: string;
  type: string;
}

export interface Video {
  id: string;
  name: string;
  duration: string;
  description: string;
  url: string;
}

export interface Lesson {
  name: string;
  description: string;
  bookingServiceId: number;
  videos: Video[];
}

export interface Lector {
  id: string;
  name: string;
  imageOffset: number;
  instagram: string;
  order: number | null;
  phoneNumber: string;
  email: string;
  slug: string;
  image: StrapiResponse<{
    alternativeText: string;
    url: string;
  }>;
  content: { text: string; title: string }[];
}

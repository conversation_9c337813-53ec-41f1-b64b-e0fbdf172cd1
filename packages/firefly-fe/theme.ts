import { createTheme } from "@mui/material";

export const colors = {
  white: "#FFFFFF",
  black: "#000000",
  fireflyGreen: "#66FFCC",
  fireflyPink: "#AB1757",
  darkGray: "#404040",
  lightGray: "#E5E5E5",
  beige: "#F6F2EF",
};

export default createTheme({
  palette: {
    primary: {
      main: colors.fireflyPink,
    },
    secondary: {
      main: colors.fireflyGreen,
    },
    info: {
      main: colors.white,
    },
    text: {
      primary: colors.darkGray,
      secondary: colors.lightGray,
    },
    background: {
      default: colors.beige,
      paper: colors.white,
    },
  },
  breakpoints: {
    values: {
      xs: 0,
      sm: 600,
      md: 900,
      lg: 1200,
      // @ts-ignore
      lgxl: 1376,
      xl: 1536,
    },
  },
  components: {
    MuiTextField: {
      styleOverrides: {
        root: {
          textarea: {
            paddingLeft: "0.75rem",
            paddingRight: "0.75rem",
            fontWeight: 700,
            fontFamily: '"Roboto","Helvetica","Arial","sans-serif"',
            "::placeholder": {
              color: colors.black,
              fontFamily: '"Roboto","Helvetica","Arial","sans-serif"',
              fontWeight: 400,
              opacity: 1,
            },
          },
          input: {
            paddingLeft: "1.5rem",
            fontWeight: 400,
            borderRadius: "2rem !important",
            fontFamily: '"Roboto","Helvetica","Arial","sans-serif"',
            backgroundColor: colors.white,
            paddingRight: "1.5rem",
            "::placeholder": {
              color: colors.black,
              fontFamily: '"Roboto","Helvetica","Arial","sans-serif"',
              fontWeight: 400,
              opacity: 1,
            },
          },
          fieldset: {
            borderWidth: 0,
            borderRadius: 0,
            borderLeft: 0,
            borderRight: 0,
            borderTop: 0,
            borderBottom: 0,
          },
          ".MuiOutlinedInput-root": {
            backgroundColor: colors.white,
            borderRadius: "2rem",
          },
          ".Mui-disabled": {
            backgroundColor: "transparent",
            paddingTop: 8,
            paddingBottom: 2,
            input: {
              "-webkit-text-fill-color": colors.white,
            },
          },
        },
      },
    },
    MuiFormLabel: {
      styleOverrides: {
        root: {
          color: `${colors.white} !important`,
          paddingLeft: 12,
        },
      },
    },
    MuiFormHelperText: {
      styleOverrides: {
        root: {
          paddingLeft: "1.5rem",
          paddingRight: "1.5rem",
          margin: 0,
          marginTop: "0.5rem",
          color: `${colors.white} !important`,
        },
      },
    },
    MuiInputAdornment: {
      styleOverrides: {
        root: {
          marginRight: "1rem",
        },
      },
    },
    MuiDialog: {
      styleOverrides: {
        paper: {
          margin: "0.25rem",
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          fontSize: "1rem",
          borderRadius: "2rem",
          // padding: ".725rem 2.5rem",
          textTransform: "initial",
          fontWeight: 700,
          fontFamily: "HrotBasic",
        },
        sizeSmall: {
          fontSize: "0.75rem",
        },
        sizeMedium: ({ theme }) => ({
          [theme.breakpoints.down("sm")]: {
            fontSize: "0.825rem",
          },
        }),
      },
    },
    MuiLink: {
      styleOverrides: {
        root: {
          textDecoration: "none",
        },
      },
    },
    MuiFormControlLabel: {
      styleOverrides: {
        root: {
          span: {
            color: colors.black,
          },
        },
      },
    },
    MuiAlert: {
      styleOverrides: {
        standardSuccess: {
          backgroundColor: colors.fireflyGreen,
          color: colors.black,
          svg: {
            color: colors.black,
          },
        },
        standardError: {
          backgroundColor: colors.fireflyPink,
          color: colors.white,
          svg: {
            color: colors.white,
          },
        },
      },
    },
    MuiTableCell: {
      styleOverrides: {
        root: () => ({
          padding: "16px 16px 16px 0",
        }),
      },
    },
    MuiRadio: {
      styleOverrides: {
        root: {
          color: colors.fireflyPink,
          "svg:nth-child(1)": {
            color: colors.fireflyPink,
          },
          "svg:nth-child(2)": {
            color: colors.fireflyGreen,
          },
        },
      },
    },
    MuiCheckbox: {
      styleOverrides: {
        root: {
          borderRadius: "50%",
          svg: {
            background: colors.fireflyPink,
            borderRadius: "50%",
            color: colors.beige,
          },
          "&.Mui-checked": {
            svg: {
              color: colors.fireflyGreen,
            },
          },
        },
      },
    },
    MuiTypography: {
      styleOverrides: {
        root: {
          color: colors.darkGray,
          fontSize: "0.875rem",
        },
        h1: ({ theme }) => ({
          [theme.breakpoints.down("sm")]: {
            fontSize: "3rem",
          },
          fontFamily: "HrotBasic",
          fontWeight: 900,
          fontSize: "min(6rem, 4vh)",
        }),
        h2: {
          fontFamily: "HrotBasic",
          fontWeight: 900,
          fontSize: "1.25rem",
        },
        h3: {
          fontFamily: "HrotBasic",
          fontWeight: 900,
        },
        h4: ({ theme }) => ({
          [theme.breakpoints.down("sm")]: {
            fontSize: "1.5rem",
          },
          fontFamily: "HrotBasic",
          fontWeight: 900,
          fontSize: "1.75rem",
        }),
        h5: {
          fontFamily: "HrotBasic",
          fontSize: "1.25rem",
          fontWeight: 900,
        },
        h6: {
          fontFamily: "HrotBasic",
          fontWeight: 900,
        },
        subtitle1: {
          fontFamily: "HrotBasic",
          fontWeight: 900,
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: ({ theme }) => ({
          backgroundColor: colors.white,
          padding: "1.5rem",
          borderRadius: "0.5rem",

          [theme.breakpoints.down("sm")]: {
            padding: "0.5rem",
          },
        }),
        elevation16: {
          borderRadius: 0,
          padding: 0,
          backgroundColor: colors.fireflyPink,
        },
        elevation1: {
          boxShadow: "none",
        },
      },
    },
    MuiTabs: {
      styleOverrides: {
        root: ({ theme }) => ({
          backgroundColor: colors.beige,
          borderRadius: "3rem",
          "& .MuiTabs-indicator": {
            display: "none",
          },
          "& .MuiTab-root.Mui-selected": {
            backgroundColor: colors.fireflyGreen,
            color: colors.black,
          },
        }),
      },
    },
    MuiTab: {
      styleOverrides: {
        root: ({ theme }) => ({
          borderRadius: "3rem",
          padding: "1.5rem",
          fontWeight: 700,
          textTransform: "capitalize",
          fontFamily: "HrotBasic",
          color: colors.black,
          fontSize: "1rem",

          [theme.breakpoints.down("sm")]: {
            padding: "0.5rem",
            fontSize: "0.75rem",
          },
        }),
      },
    },
  },
});

{"name": "firefly-fe", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@date-io/date-fns": "^2.15.0", "@emotion/react": "^11.9.3", "@emotion/styled": "^11.9.3", "@hookform/resolvers": "^2.9.7", "@mui/icons-material": "^5.8.4", "@mui/material": "^5.9.0", "@mui/x-date-pickers": "^5.0.1", "@svgr/webpack": "^6.3.1", "@tanstack/react-query": "^4.0.10", "axios": "^0.27.2", "date-fns": "^2.29.2", "js-cookie": "^3.0.1", "mapbox-gl": "^2.10.0", "markdown-to-jsx": "^7.1.7", "moment-timezone": "^0.5.43", "next": "12.2.2", "nookies": "^2.5.2", "rambda": "^7.2.1", "react": "18.2.0", "react-device-detect": "^2.2.2", "react-dom": "18.2.0", "react-ga4": "^2.1.0", "react-hook-form": "^7.34.2", "react-lines-ellipsis": "^0.15.1", "react-map-gl": "^7.0.19", "react-spring": "^9.7.1", "react-use": "^17.4.0", "sharp": "^0.33.5", "yup": "^0.32.11"}, "devDependencies": {"@tanstack/react-query-devtools": "^4.0.10", "@types/node": "18.0.3", "@types/react": "18.0.15", "@types/react-dom": "18.0.6", "eslint": "8.19.0", "eslint-config-next": "12.2.2", "typescript": "4.7.4"}}
export interface ReservantoErrors {
  IsError: boolean;
  ErrorParameter?: string | null;
  ErrorMessage?: string | null;
  ErrorParameters?: string[] | null;
  ErrorMessages?: string[] | null;
}

export interface Class {
  Id: number;
  StartDate: number;
  EndDate: number;
  BookingResourceId: number;
  BookingResourceName: string;
  BookingServiceId: number;
  BookingServiceName: string;
  CourseId: number | null;
  CalendarId: number;
  CalendarName: string;
  IsAvailable: boolean;
  Capacity: number;
  OccupiedCapacity: number;
  FormattedAvailability: string;
}

export interface Usage {
  BookingServiceId: number;
  Count: number;
  Used: number;
  PassUsageId: number;
}

interface BoughtPass {
  ActiveFrom: number;
  AggregatedCount: number | null;
  CreatedAt: number;
  DurationMinutes: number;
  ExpirationDate: number;
  IsIntermissionActive: boolean;
  PassId: number;
  PassName: string;
  PassToCustomerId: number;
  Usages: Usage[];
}

export interface Customer {
  Id?: number;
  CratedAt?: number;
  Name?: string;
  Phone?: string;
  Email?: string;
  Deleted?: boolean;
  role?: "Guest" | "LoggedUser";
  PriceLevelId?: number;
  PriceLevelName?: string;
  LoyaltyPointsCanBeUsed?: boolean;
  LoyaltyPointsCash?: string;
  Credit?: number;
  LoyaltyPoints?: string;
  BoughtPasses?: BoughtPass[];
}

export interface Transaction {
  CreatedAt: number;
  Value: number;
  Note: string;
  Id: number;
}

export interface CreditTransactions {
  Customer: string;
  CurrentCredit: string;
  Transactions: Transaction[];
}

export interface AlternateEvent {
  AppointmentId: number;
  StartDate: number;
  BookingServiceId: number;
  SourceName: string;
  EndDate: number;
  ServiceName: string;
  CourseId?: number | null;
  Price: number;
  CalendarId: number;
  CalendarName: string;
  Status: "waiting";
}

export interface Event {
  AppointmentId: number;
  StartDate: number;
  EndDate: number;
  MerchantNote: string;
  BookingServiceId: number;
  PaymentMethodFormatted: string | null;
  LocationName: string;
  SourceName: string;
  SourceId: number;
  ServiceName: string;
  SegmentName: string;
  CustomerId: number;
  CustomerFullName: string;
  CourseId?: number | null;
  CustomerNote: string;
  Price: number;
  PaidPartOfPrice: number;
  CalendarId: number;
  CalendarName: string;
  IsPaid: boolean;
  Count: number;
  NotShowStatus: number;
  Status: "booked";
}

export interface CourseCustomerEvents {
  TotalCoursePriceWithVat: number;
  PaidPartOfPriceWithVat: number;
  IsPaid: boolean;
  TotalLections: number;
  LectionsOccupiedByCustomer: number;
}

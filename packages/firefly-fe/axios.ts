import axios from "axios";
import { refreshAccessToken } from "./components/user/query";
import { isSSR } from "./helpers/image";
import { ACCESS_TOKEN_KEY, REFRESH_TOKEN_KEY } from "./providers/MeProvider";
import { ReservantoErrors } from "./reservanto/types";
import Cookies from "js-cookie";

export interface StrapiResponse<T> {
  data: {
    id: number;
    attributes: T;
  };
  meta: any;
}

export interface StrapiArrayResponse<T> {
  data: {
    id: number;
    attributes: T;
  }[];
  meta: any;
}

export interface ReservantoResponse<T> extends ReservantoErrors {
  Items: T;
}

export enum ReservantoAppVersion {
  "v1" = "v1",
}

export const axiosApi = axios.create({
  baseURL: isSSR ? process.env.NEXT_PUBLIC_API_URL : "/api",
});

axiosApi.interceptors.response.use(undefined, async (error) => {
  const originalRequest = error.config;
  if (error?.response?.status === 401) {
    try {
      const refreshToken = Cookies.get(REFRESH_TOKEN_KEY);
      if (refreshToken) {
        const response = await refreshAccessToken(refreshToken || "");
        Cookies.set(ACCESS_TOKEN_KEY, response.accessToken);
        if (response.accessToken && originalRequest.url !== "/refresh-token") {
          originalRequest._retry = true;
          return axiosApi(originalRequest);
        }
      }
      return Promise.reject(new Error("Error obtaining accessToken"));
    } catch (e) {
      console.log(e);
      return Promise.reject(e);
    }
  }
  return Promise.reject(error);
});

export const axiosCms = axios.create({
  baseURL: isSSR ? `${process.env.NEXT_PUBLIC_STRAPI_URL}/api` : "/cms",
});

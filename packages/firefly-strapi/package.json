{"name": "firefly-strapi", "private": true, "version": "0.1.0", "description": "A Strapi application", "scripts": {"develop": "strapi develop", "start": "NODE_ENV=production strapi start", "build": "NODE_ENV=production strapi build", "strapi": "strapi"}, "dependencies": {"@strapi/plugin-i18n": "^4.24.1", "@strapi/plugin-users-permissions": "^4.24.1", "@strapi/provider-upload-cloudinary": "^4.24.1", "@strapi/strapi": "^4.24.1", "better-sqlite3": "9.6.0", "pg": "^8.7.3", "pg-connection-string": "^2.5.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^5.3.4", "strapi-plugin-populate-deep": "^3.0.1", "styled-components": "^5.2.1"}, "author": {"name": "A Strapi developer"}, "strapi": {"uuid": "7e2d3deb-fe7a-43e3-b160-e5a847645f6c"}, "engines": {"node": ">=16.x.x <=20.x.x", "npm": ">=6.0.0"}, "license": "MIT"}
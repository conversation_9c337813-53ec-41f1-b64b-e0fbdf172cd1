{"kind": "collectionType", "collectionName": "lectors", "info": {"singularName": "lector", "pluralName": "lectors", "displayName": "<PERSON><PERSON>", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"name": {"type": "string"}, "email": {"type": "email"}, "instagram": {"type": "string"}, "phoneNumber": {"type": "string"}, "image": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images"]}, "coloredImage": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images"]}, "content": {"type": "component", "repeatable": true, "component": "global.pharagraph"}, "slug": {"type": "uid", "targetField": "name"}, "imageOffset": {"type": "integer"}, "order": {"type": "integer"}}}
{"kind": "collectionType", "collectionName": "lesson_types", "info": {"singularName": "lesson-type", "pluralName": "lesson-types", "displayName": "LessonType", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"title": {"type": "string"}, "description": {"type": "richtext"}, "type": {"type": "enumeration", "enum": ["course", "openClass", "openSpace", "private<PERSON><PERSON><PERSON>"]}}}
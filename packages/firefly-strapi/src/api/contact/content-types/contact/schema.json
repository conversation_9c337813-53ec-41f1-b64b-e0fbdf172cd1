{"kind": "singleType", "collectionName": "contacts", "info": {"singularName": "contact", "pluralName": "contacts", "displayName": "Contact", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"receptionPhone": {"type": "string"}, "email": {"type": "string"}, "instagram": {"type": "string"}, "facebook": {"type": "string"}, "receptionOpenHours": {"type": "string"}, "studioManagerPhone": {"type": "string"}, "contactInfo": {"displayName": "ContactInfo", "type": "component", "repeatable": true, "component": "global.contact-info"}, "locations": {"displayName": "location", "type": "component", "repeatable": true, "component": "global.location"}, "englishContentInfo": {"type": "richtext"}, "englishTitleInfo": {"type": "string"}, "destinationDescription": {"type": "text"}}}
{"kind": "collectionType", "collectionName": "lessons", "info": {"singularName": "lesson", "pluralName": "lessons", "displayName": "Lesson", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"name": {"type": "string"}, "description": {"type": "richtext"}, "bookingServiceId": {"type": "integer"}, "price": {"type": "decimal"}, "duration": {"type": "integer"}, "lesson_type": {"type": "relation", "relation": "oneToOne", "target": "api::lesson-type.lesson-type"}, "isPromoted": {"type": "boolean"}, "isForBeginners": {"type": "boolean"}, "videos": {"displayName": "video", "type": "component", "repeatable": true, "component": "global.video"}, "activity": {"type": "relation", "relation": "manyToOne", "target": "api::activity.activity", "inversedBy": "lessonsDeprecated"}, "activities": {"type": "relation", "relation": "manyToMany", "target": "api::activity.activity", "inversedBy": "lessons"}}}
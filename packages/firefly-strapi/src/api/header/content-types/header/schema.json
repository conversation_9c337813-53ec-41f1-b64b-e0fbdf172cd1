{"kind": "singleType", "collectionName": "headers", "info": {"singularName": "header", "pluralName": "headers", "displayName": "Header", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"activityLinks": {"type": "component", "repeatable": true, "component": "global.link"}, "otherLinks": {"type": "component", "repeatable": true, "component": "global.link"}}}
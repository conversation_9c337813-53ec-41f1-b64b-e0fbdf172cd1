{"kind": "singleType", "collectionName": "homepages", "info": {"singularName": "homepage", "pluralName": "homepages", "displayName": "Homepage", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"title": {"type": "string"}, "description": {"type": "text"}, "news": {"type": "richtext"}, "activities": {"type": "relation", "relation": "oneToMany", "target": "api::activity.activity"}, "secondTitle": {"type": "string"}, "secondDescription": {"type": "text"}, "thirdTitle": {"type": "string"}, "thirdDescription": {"type": "text"}, "secondImage": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images"]}, "thirdImage": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images"]}, "lesson_types": {"type": "relation", "relation": "oneToMany", "target": "api::lesson-type.lesson-type"}, "destinationDescription": {"type": "string"}}}
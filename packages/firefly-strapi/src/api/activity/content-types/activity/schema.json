{"kind": "collectionType", "collectionName": "activities", "info": {"singularName": "activity", "pluralName": "activities", "displayName": "Activity", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"title": {"type": "string"}, "shortDescription": {"type": "text"}, "news": {"type": "richtext"}, "description": {"type": "text"}, "image": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images"]}, "imagePreview": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images"]}, "slug": {"type": "uid", "targetField": "title"}, "lesson_types": {"type": "relation", "relation": "oneToMany", "target": "api::lesson-type.lesson-type"}, "tryItText": {"type": "text"}, "lectors": {"type": "relation", "relation": "oneToMany", "target": "api::lector.lector"}, "lessonsDeprecated": {"type": "relation", "relation": "oneToMany", "target": "api::lesson.lesson", "mappedBy": "activity"}, "lessons": {"type": "relation", "relation": "manyToMany", "target": "api::lesson.lesson", "mappedBy": "activities"}}}